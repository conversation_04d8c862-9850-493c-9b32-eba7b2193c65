export interface LoggingInfoInterface {
  depthFrom?: number;
  depthTo?: number;
  dataEntryValues: any[];
  drillholeId?: number;
  geologySuiteId?: number;
}

export interface MultiLoggingInfoInterface {
  suiteId: number;
  suiteName: string;
  drillHoleId: string;
  entries: LoggingInfoInterface[];
}

export interface LoggingLineData {
  x: number;
  id: number;
}

export interface LoggingLine {
  isSave: boolean;
  id: number;
  data: LoggingLineData;
}

export interface LoggingCreateBody {
  description: string;
  rockTypeId?: number;
  code: string;
  depthFrom?: number;
  depthTo?: number;
}

export interface LoggingEditBody {
  description: string;
  rockTypeId?: number;
  code?: string;
  depthFrom?: number;
  depthTo?: number;
  groupId: string;
  projectId: number;
  drillHoleName: string;
  geologySuiteId: number;
  geologyAttribute: string;
}

export interface LoggingContextMenu {
  visible: boolean;
  // to disable left click
  isRightClick: boolean;
  x?: number;
  y?: number;
}

export interface PreviewImage {
  visible: boolean;
  url?: string;
}
