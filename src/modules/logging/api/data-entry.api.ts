import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";

export interface DataEntryBody {
  id?: number;
  geologySuiteId: number;
  drillholeId: number;
  depthFrom?: any;
  depthTo?: any;
  dataEntryValues?: any[];
}
export interface DataEntryValueBody {
  colour: any;
  colourId: any;
  dataEntryId: any;
  dateValue: any;
  description: any;
  fieldName: any;
  fieldType: any;
  geologysuiteFieldId: any;
  number: any;
  numberId: any;
  numberValue: any;
  pickListItem: any;
  pickListItemId: any;
  rockNode: any;
  rockNodeId: any;
  rockType: any;
  rockTypeId: any;
  sequence: number;
  treeNode: any;
  valueId: any;
}

export interface LoggingBarItem {
  id: number;
  geologySuiteId: number;
  drillHoleId: number;
  imageSubtypeId: number;
  depthFrom: number;
  startImageCropId: number;
  startX: number;
  depthTo: number;
  endImageCropId: number;
  endX: number;
  betweenImageCropIds: number[];
  dataEntryId?: number; // Add dataEntryId to link with data entry
}

export interface LoggingBarResponse {
  items: LoggingBarItem[];
  totalCount: number;
}
const dataEntryRequest = {
  getMultipleDataEntryByDrillholes: async (params: {
    geologySuiteId: number;
    drillholeIds: string; // Array of IDs as string "[1,2,3]"
    geologySuiteFieldIds?: string; // Array of IDs as string "[1]"
    sortOrder: string;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetAllDataEntryByDrillhole`,
        params,
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  create: async (body: any) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/DataEntry/CreateDataEntry`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getDataEntryById: async (params: { dataEntryId: number }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetDetailDataEntry`,
        params,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getBlockBySelectedPoint: async (params: {
    geologySuiteId: number;
    drillHoleId: number;
    imageCropId: number;
    x: number;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetBlockBySelectedPoint`,
        params,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getNextBlock: async (params: {
    geologySuiteId: number;
    drillHoleId: number;
    depthFrom: number;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetNextBlock`,
        params,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getAllDataEntry: async (params: {
    GeologySuiteId?: number;
    DrillholeName?: string;
    DrillholeId?: number;
    DepthFrom?: number;
    DepthTo?: number;
    skipCount?: number;
    maxResultCount?: number;
    sortOrder?: string;
    keyWord?: string;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetAllDataEntry`,
        {
          ...params,
          maxResultCount: params?.maxResultCount ?? 50, // Giảm từ 1000 xuống 50
        },
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10),
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/DataEntry/DeleteGeologyDataPoint?Id=${params.id}`,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: any) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDataEntry`,
        body,
      );

      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateDepth: async (body: {
    id: number;
    depthFrom: number | null;
    depthTo: number | null;
  }) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDepth`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateDepthDataEntry: async (body: {
    drillholeId: number;
    geologySuiteId: number;
    dataEntries: {
      id: number;
      depthFrom: any;
      depthTo: any;
    }[];
  }) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDepthDataEntry`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateDataEntryValue: async (body: {
    valueId: number;
    fieldType: number;
    colorId?: number;
    dateValue?: string;
    description?: string;
    numberValue?: number;
    pickListItemId?: number;
    rockTypeId?: number;
  }) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDataEntryValue`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getAllLoggingBar: async (params: {
    GeologySuiteId: number;
    DrillholeId: number;
    ImageSubtypeId?: number;
    SkipCount?: number;
    MaxResultCount?: number;
    DepthFrom?: number;
    DepthTo?: number;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetAllLoggingBar`,
        {
          ...params,
          SkipCount: params?.SkipCount ?? 0,
          MaxResultCount: params?.MaxResultCount ?? 50, // Giảm từ 1000 xuống 50
        },
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          totalCount: response?.result?.totalCount,
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  calculateLoggingBar: async (body: {
    geologySuiteId: number;
    drillHoleId: number;
    imageSubtypeId: number;
    imageTypeId: number;
  }) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/DataEntry/CalculateLoggingBar`,
        body,
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default dataEntryRequest;
