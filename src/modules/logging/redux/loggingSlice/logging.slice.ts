/* eslint-disable max-lines */
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { isNumber } from "lodash";

import { RequestState } from "@/common/configs/app.contants";

import { LoggingBarItem } from "../../api/data-entry.api";
import { CONFIG } from "../../constants/logging.constants";
import {
  LoggingContextMenu,
  LoggingInfoInterface,
  LoggingLine,
  PreviewImage,
} from "../../interface/logging.interface";
import { SegmentationResult } from "../../model/dtos/logging.config";
import {
  EnumCalculateClickedDepth,
  EnumLoggingExtraViews,
  EnumLoggingViews,
} from "../../model/enum/logging.enum";
import {
  RockLineCalculationResult,
  RockLineType,
} from "../../types/logging.types";
import {
  getExtraViewModeImages,
  getGeologyData,
  getLoggingBar,
  getLoggingInfo,
  getLoggingInfoInfinite,
} from "./thunks";

const initialState: LoggingSliceState = {
  allLoggings: [],
  allLoggingsTable: [],
  infiniteScrollLoggings: [], // Thêm state riêng cho infinite scroll
  reviewDepthFrom: 0,
  lines: [],
  legend: false,
  rqdCalculationResult: [],
  depthFrom: 0,
  depthTo: 0,
  skipCount: 0,
  infiniteScrollState: {
    currentPage: 0,
    totalItems: 0,
    hasMore: true,
    loading: false,
  },
  contextMenu: {
    visible: false,
    isRightClick: false,
  },
  hasShownMeasureInstructions: false,
  previewImage: {
    visible: false,
    url: "",
  },
  imageRows: [],
  imageExtraRows: [],
  extraImageTypeId: undefined,
  extraImageSubtypeId: undefined,

  // Main image type and subtype for logging
  selectedImageTypeId: undefined,
  selectedImageSubtypeId: undefined,
  availableImageSubtypes: [],

  extraViews: [],
  loggingViewMode: EnumLoggingViews.Image,
  segmentations: [],
  isShowSegmentation: false,
  isShowPercentageRecovery: false,
  calculateClickedDepth: EnumCalculateClickedDepth.ApiCalculation,

  imageGap: 200,
  refetchLoggingView: false,
  displayColumn: CONFIG.DEFAULT_DISPLAY_COLUMN,
  offsetY: undefined,
  targetDepth: undefined,
  loggingSuiteMode: "Geology",
  openModalGeotechData: false,
  geotechData: [],
  geotechDataToEdit: null,
  refetchGeotechData: false,
  startPointLineInterval: null,
  measurePointsInterval: {},
  measureXPointsInterval: {},
  loadingGeotechData: false,
  recoveries: [],
  trayDepths: [],
  toggleUpdateOCR: false,
  isSplitLine: false,
  isMergeLine: false,
  isDeleteLine: false,
  isJoinLine: false,
  isRemoveRock: false,
  selectedLine: null,
  selectedRockLineType: RockLineType.Recovery,
  isAddLine: false,
  loggingSuiteSelected: null,
  geotechSelected: null,
  segmentationsDetail: [],

  // Logging bar data state
  loggingBarData: [],
  loggingBarSuite: undefined,

  // Pixel color picker mode
  isPixelColorPickerEnabled: false,
  rockGroupDetail: null,

  // Rock line length display
  isShowRockLineLength: false,

  fetchDataEntry: false,
};

export const loggingSlice = createSlice({
  name: "logging",
  initialState,
  reducers: {
    initLogging: (state) => {
      state.reviewDepthTo = undefined;
      state.lines = [];
    },

    updateReviewDepthFrom: (state, action: PayloadAction<number | null>) => {
      state.reviewDepthFrom = action.payload;
    },
    updateImageGap: (state, action: PayloadAction<number>) => {
      state.imageGap = action.payload;
    },
    updateCalculateClickedDepth: (
      state,
      action: PayloadAction<EnumCalculateClickedDepth>,
    ) => {
      state.calculateClickedDepth = action.payload;
    },
    updateIsShowSegmentation: (state, action: PayloadAction<boolean>) => {
      state.isShowSegmentation = action.payload;
    },
    updateIsShowPercentageRecovery: (state, action: PayloadAction<boolean>) => {
      state.isShowPercentageRecovery = action.payload;
    },
    updateSelectedDrilhole: (state, action: PayloadAction<any>) => {
      state.selectedDrillHole = action.payload;
    },

    updatePreviousLoggingDepth: (state, action: PayloadAction<number>) => {
      state.previousLoggingDepth = action.payload;
    },

    updateReviewDepthTo: (state, action: PayloadAction<number>) => {
      state.reviewDepthTo = action.payload;
    },
    updateLoggingViewMode: (state, action: PayloadAction<EnumLoggingViews>) => {
      state.loggingViewMode = action.payload;
    },

    updateLoggingLine: (state, action: PayloadAction<LoggingLine>) => {
      const index = state.lines.findIndex(
        (line) => line.id === action.payload.id,
      );
      if (index !== -1) {
        state.lines[index] = action.payload;
      }
    },
    addLoggingLine: (state, action: PayloadAction<LoggingLine>) => {
      state.lines = [...state.lines, action.payload];
    },

    updateSegmentsResult: (
      state,
      action: PayloadAction<SegmentationResult[]>,
    ) => {
      state.segmentations = action.payload;
    },
    updateSegmentsDetailResult: (
      state,
      action: PayloadAction<SegmentationResult[]>,
    ) => {
      state.segmentationsDetail = action.payload;
    },

    saveLoggingLine: (state) => {
      state.lines = state.lines.map((line) => ({
        ...line,
        isSave: true,
      }));
    },
    updateGeologySuiteBar: (state, action: PayloadAction<any>) => {
      state.geologySuiteBar = action.payload;
    },
    updateCurrentDepth: (
      state,
      action: PayloadAction<{ depthFrom?: number; depthTo?: number }>,
    ) => {
      if (isNumber(action.payload.depthFrom)) {
        state.depthFrom = action.payload.depthFrom;
      }
      if (isNumber(action.payload.depthTo)) {
        state.depthTo = action.payload.depthTo;
      }
    },

    saveLoggingData: (state, action: PayloadAction<LoggingInfoInterface>) => {
      state.reviewDepthFrom = action.payload.depthTo ?? 0;
      state.reviewDepthTo = undefined;
    },

    updateLoggingContextMenu: (
      state,
      action: PayloadAction<Partial<LoggingContextMenu>>,
    ) => {
      state.contextMenu = {
        ...state.contextMenu,
        ...action.payload,
      };
    },

    updatePreviewImageUrl: (
      state,
      action: PayloadAction<Partial<PreviewImage>>,
    ) => {
      state.previewImage = {
        ...state.previewImage,
        ...action.payload,
      };
    },

    setImageRows: (state, action: PayloadAction<any[]>) => {
      state.imageRows = action.payload;
    },

    setImageHyperRows: (state, action: PayloadAction<any[]>) => {
      state.imageExtraRows = action.payload;
    },

    updateExtraViews: (
      state,
      action: PayloadAction<EnumLoggingExtraViews[]>,
    ) => {
      state.extraViews = action.payload;
    },
    updateSelectedSuite: (state, action: PayloadAction<string>) => {
      state.selectedSuite = action.payload;
    },
    updateRefetchLoggingView: (state) => {
      if (state.refetchLoggingView) {
        state.refetchLoggingView = false;
      } else {
        state.refetchLoggingView = true;
      }
    },
    updateLoggingSuiteMode: (
      state,
      action: PayloadAction<"Geology" | "Geotech" | null>,
    ) => {
      state.loggingSuiteMode = action.payload;
    },
    updateSkipCount: (state, action: PayloadAction<number>) => {
      state.skipCount = action.payload;
    },
    clearLoggingData: () => {
      return initialState;
    },
    updateDisplayColumn: (
      state,
      action: PayloadAction<typeof CONFIG.DEFAULT_DISPLAY_COLUMN>,
    ) => {
      state.displayColumn = action.payload;
    },
    updateOffsetY: (
      state,
      action: PayloadAction<number | { offsetY: number; targetDepth: number }>,
    ) => {
      if (typeof action.payload === "number") {
        state.offsetY = action.payload;
      } else {
        state.offsetY = action.payload.offsetY;
        state.targetDepth = action.payload.targetDepth;
      }
    },
    updateHasShownMeasureInstructions: (
      state,
      action: PayloadAction<boolean>,
    ) => {
      state.hasShownMeasureInstructions = action.payload;
    },
    updateOpenModalGeotechData: (state, action: PayloadAction<boolean>) => {
      if (!action.payload) {
        state.measurePointsInterval = {};
      }
      state.openModalGeotechData = action.payload;
    },
    updateCalculateRecoveryBySelectPoint: (
      state,
      action: PayloadAction<{
        imageCropId: number;
        x: number;
        depth: number;
      }>,
    ) => {
      state.imageCropId = action.payload.imageCropId;
      state.x = action.payload.x;
      state.depth = action.payload.depth;
    },
    updateLoggingSuiteId: (state, action: PayloadAction<number>) => {
      state.loggingSuiteId = action.payload;
    },
    updateGeotechData: (state, action: PayloadAction<any[]>) => {
      state.geotechData = action.payload;
    },
    updateGeotechDataToEdit: (state, action: PayloadAction<any>) => {
      state.geotechDataToEdit = action.payload;
    },
    updateRefetchGeotechData: (state) => {
      state.refetchGeotechData = !state.refetchGeotechData;
    },
    updateStartPointLineInterval: (
      state,
      action: PayloadAction<{
        x: number;
        rowIndex: number;
      } | null>,
    ) => {
      state.startPointLineInterval = action.payload;
    },
    updateMeasurePointsInterval: (
      state,
      action: PayloadAction<{
        start?: { x: number; depth: number; imageCropId?: string };
        end?: { x: number; depth: number; imageCropId?: string };
      }>,
    ) => {
      state.measurePointsInterval = action.payload;
    },
    updateMeasureXPointsInterval: (
      state,
      action: PayloadAction<{
        start?: { x: number; depth: number };
        end?: { x: number; depth: number };
      }>,
    ) => {
      state.measureXPointsInterval = action.payload;
    },
    updateLoadingGeotechData: (state, action: PayloadAction<boolean>) => {
      state.loadingGeotechData = action.payload;
    },
    updateRecoveries: (
      state,
      action: PayloadAction<RockLineCalculationResult[]>,
    ) => {
      state.recoveries = action.payload;
    },
    updateTrayDepths: (
      state,
      action: PayloadAction<
        Array<{
          drillHoleId: number;
          trayNumber: number;
          startDepth: number;
          endDepth: number;
          id: number;
        }>
      >,
    ) => {
      state.trayDepths = action.payload;
    },
    updateToggleUpdateOCR: (state) => {
      state.toggleUpdateOCR = !state.toggleUpdateOCR;
    },
    updateIsSplitLine: (state, action: PayloadAction<boolean>) => {
      state.isSplitLine = action.payload;
      // When enabling Split mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isMergeLine = false;
        state.isDeleteLine = false;
        state.isAddLine = false;
        state.isJoinLine = false;
        state.isRemoveRock = false;
        state.selectedLine = null;
      } else {
        state.selectedLine = null;
      }
    },
    updateIsMergeLine: (state, action: PayloadAction<boolean>) => {
      state.isMergeLine = action.payload;
      // When enabling Merge mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isSplitLine = false;
        state.isDeleteLine = false;
        state.isAddLine = false;
        state.isJoinLine = false;
        state.isRemoveRock = false;
        state.selectedLine = null;
      } else {
        state.selectedLine = null;
      }
    },
    updateIsDeleteLine: (state, action: PayloadAction<boolean>) => {
      state.isDeleteLine = action.payload;
      // When enabling Delete mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isSplitLine = false;
        state.isMergeLine = false;
        state.isAddLine = false;
        state.isJoinLine = false;
        state.isRemoveRock = false;
        state.selectedLine = null;
      } else {
        state.selectedLine = null;
      }
    },
    updateIsAddLine: (state, action: PayloadAction<boolean>) => {
      state.isAddLine = action.payload;
      // When enabling Add mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isSplitLine = false;
        state.isMergeLine = false;
        state.isDeleteLine = false;
        state.isJoinLine = false;
        state.isRemoveRock = false;
        state.selectedLine = null;
      }
      // No need to reset selectedLine as Add mode doesn't use it
    },
    updateIsJoinLine: (state, action: PayloadAction<boolean>) => {
      state.isJoinLine = action.payload;
      // When enabling Join mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isSplitLine = false;
        state.isMergeLine = false;
        state.isDeleteLine = false;
        state.isAddLine = false;
        state.isRemoveRock = false;
        state.selectedLine = null;
      } else {
        state.selectedLine = null;
      }
    },
    updateIsRemoveRock: (state, action: PayloadAction<boolean>) => {
      state.isRemoveRock = action.payload;
      // When enabling Remove Rock mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isSplitLine = false;
        state.isMergeLine = false;
        state.isDeleteLine = false;
        state.isJoinLine = false;
        state.isAddLine = false;
        state.selectedLine = null;
      } else {
        state.selectedLine = null;
      }
    },
    updateSelectedLine: (state, action: PayloadAction<any>) => {
      state.selectedLine = action.payload;
    },
    updateSelectedRockLineType: (
      state,
      action: PayloadAction<RockLineType>,
    ) => {
      state.selectedRockLineType = action.payload;
    },
    updateLoggingSuiteSelected: (state, action: PayloadAction<any>) => {
      state.geotechData = [];
      state.loggingSuiteSelected = action.payload;
    },
    updateGeotechSelected: (state, action: PayloadAction<any>) => {
      state.geotechSelected = action.payload;
    },
    updateAllLoggingsTable: (state, action: PayloadAction<any[]>) => {
      state.allLoggingsTable = action.payload;
    },
    resetInfiniteScrollState: (state) => {
      state.infiniteScrollState = {
        currentPage: 0,
        totalItems: 0,
        hasMore: true,
        loading: false,
      };
      state.allLoggings = [];
    },
    updateInfiniteScrollState: (
      state,
      action: PayloadAction<Partial<typeof state.infiniteScrollState>>,
    ) => {
      state.infiniteScrollState = {
        ...state.infiniteScrollState,
        ...action.payload,
      };
    },
    updateExtraviewImageType: (
      state,
      action: PayloadAction<number | undefined>,
    ) => {
      state.extraImageTypeId = action.payload;
    },
    updateExtraviewImageSubtype: (
      state,
      action: PayloadAction<number | undefined>,
    ) => {
      state.extraImageSubtypeId = action.payload;
    },

    // Main image type and subtype actions
    updateSelectedImageType: (
      state,
      action: PayloadAction<number | undefined>,
    ) => {
      state.selectedImageTypeId = action.payload;
      // Reset subtype when image type changes
      if (action.payload !== state.selectedImageTypeId) {
        state.selectedImageSubtypeId = undefined;
      }
    },
    updateSelectedImageSubtype: (
      state,
      action: PayloadAction<number | undefined>,
    ) => {
      state.selectedImageSubtypeId = action.payload;
    },
    updateAvailableImageSubtypes: (state, action: PayloadAction<any[]>) => {
      state.availableImageSubtypes = action.payload;
    },
    updateLegend: (state, action: PayloadAction<boolean>) => {
      state.legend = action.payload;
    },
    updateRqdCalculationResult: (
      state,
      action: PayloadAction<RockLineCalculationResult[]>,
    ) => {
      state.rqdCalculationResult = action.payload;
    },

    // Logging bar actions
    updateLoggingBarData: (state, action: PayloadAction<LoggingBarItem[]>) => {
      state.loggingBarData = action.payload;
    },
    updateLoggingBarSuite: (
      state,
      action: PayloadAction<string | undefined>,
    ) => {
      state.loggingBarSuite = action.payload;
    },
    clearLoggingBarData: (state) => {
      state.loggingBarData = [];
    },

    togglePixelColorPicker: (state) => {
      state.isPixelColorPickerEnabled = !state.isPixelColorPickerEnabled;
      // Hide rock segments when pixel color picker is enabled
      if (state.isPixelColorPickerEnabled) {
        state.isShowSegmentation = false;
        state.isShowPercentageRecovery = false;
      }
    },
    updateRockGroupDetail: (state, action: PayloadAction<any>) => {
      state.rockGroupDetail = action.payload;
    },
    toggleRockLineLength: (state) => {
      state.isShowRockLineLength = !state.isShowRockLineLength;
    },
    updateFetchDataEntry: (state) => {
      state.fetchDataEntry = !state.fetchDataEntry;
    },
  },

  extraReducers: (builder) => {
    builder
      .addCase(getGeologyData.pending, (state) => {
        state.getGeoDataStatus = RequestState.pending;
      })
      .addCase(getGeologyData.fulfilled, (state, action) => {
        state.getGeoDataStatus = RequestState.success;
        const loggingDatas = action.payload?.items ?? [];

        state.allLoggings = loggingDatas;
        state.reviewDepthTo = undefined;
        const loggingLines: LoggingLine[] = (state.allLoggings as any)
          .map((logging) => {
            const findImageRow = state.imageRows.find(
              (imageRow) =>
                logging?.depthTo >= imageRow.depthFrom &&
                logging?.depthTo <= imageRow.depthTo,
            );
            if (!findImageRow) {
              return;
            }
            return {
              id: findImageRow?.id,
              isSave: true,
              data: {
                x:
                  ((logging?.depthTo - findImageRow.depthFrom) /
                    (findImageRow.depthTo - findImageRow.depthFrom)) *
                  findImageRow.coordinate.Width,
                id: findImageRow.id,
              },
            } as LoggingLine;
          })
          .filter(Boolean);
        state.lines = loggingLines;
      })

      .addCase(getExtraViewModeImages.pending, (state) => {
        state.getExtraRowStatus = RequestState.pending;
      })
      .addCase(getExtraViewModeImages.fulfilled, (state, action) => {
        state.getExtraRowStatus = RequestState.success;
        state.imageExtraRows = (action.payload?.items?.[0]?.croppedImages ?? [])
          ?.map((item: any) => {
            return {
              ...item,
              coordinate: JSON.parse(item.coordinate),
            };
          })
          .filter((image) => image?.type?.toLowerCase() === "row");
      })
      .addCase(getLoggingInfo.pending, (state) => {
        state.getLoggingInfoStatus = RequestState.pending;
      })
      .addCase(getLoggingInfo.fulfilled, (state, action) => {
        state.getLoggingInfoStatus = RequestState.success;
        state.allLoggings = action.payload?.items ?? [];
      })
      .addCase(getLoggingInfoInfinite.pending, (state) => {
        if (!state.infiniteScrollState) {
          state.infiniteScrollState = {
            currentPage: 0,
            totalItems: 0,
            hasMore: true,
            loading: false,
          };
        }
        state.infiniteScrollState.loading = true;
        state.getLoggingInfoStatus = RequestState.pending;
      })
      .addCase(getLoggingInfoInfinite.fulfilled, (state, action) => {
        if (!state.infiniteScrollState) {
          state.infiniteScrollState = {
            currentPage: 0,
            totalItems: 0,
            hasMore: true,
            loading: false,
          };
        }

        const { items = [], pagination, reset } = action.payload || {};

        // Sort theo depthFrom giảm dần (cao → thấp)
        const sortItemDesc = (a: any, b: any) => {
          return b.depthFrom - a.depthFrom;
        };
        const sortedItems = items.sort(sortItemDesc);

        if (reset) {
          // Reset data for initial load
          state.infiniteScrollLoggings = sortedItems;
          state.infiniteScrollState.currentPage = 0;
        } else {
          // Append data for infinite scroll
          state.infiniteScrollLoggings = [
            ...(state.infiniteScrollLoggings || []),
            ...sortedItems,
          ];
          state.infiniteScrollState.currentPage += 1;
        }

        state.infiniteScrollState.totalItems = pagination?.total ?? 0;
        state.infiniteScrollState.hasMore =
          (state.infiniteScrollLoggings || []).length <
          (pagination?.total ?? 0);
        state.infiniteScrollState.loading = false;
        state.getLoggingInfoStatus = RequestState.success;
      })
      .addCase(getLoggingInfoInfinite.rejected, (state) => {
        if (!state.infiniteScrollState) {
          state.infiniteScrollState = {
            currentPage: 0,
            totalItems: 0,
            hasMore: true,
            loading: false,
          };
        }
        state.infiniteScrollState.loading = false;
        state.getLoggingInfoStatus = RequestState.error;
      })
      .addCase(getLoggingBar.fulfilled, (state, action) => {
        state.loggingBarData = action.payload?.items || [];
      });
  },
});

export interface LoggingSliceState {
  allLoggings: any[];
  allLoggingsTable: any[];
  infiniteScrollLoggings: any[]; // State riêng cho infinite scroll
  previousLoggingDepth?: number;
  reviewDepthFrom: number | null;
  reviewDepthTo?: number | null;
  lines: LoggingLine[];
  depthFrom: number;
  depthTo: number;
  legend: boolean;
  infiniteScrollState: {
    currentPage: number;
    totalItems: number;
    hasMore: boolean;
    loading: boolean;
  };
  contextMenu: LoggingContextMenu;
  hasShownMeasureInstructions: boolean;
  previewImage: PreviewImage;
  getGeoDataStatus?: RequestState;
  imageRows: any[];
  getExtraRowStatus?: RequestState;
  imageExtraRows: any[];
  extraImageTypeId?: number | undefined;
  extraImageSubtypeId?: number | undefined;

  // Main image type and subtype for logging
  selectedImageTypeId?: number | undefined;
  selectedImageSubtypeId?: number | undefined;
  availableImageSubtypes?: any[];

  extraViews: EnumLoggingExtraViews[];
  selectedDrillHole?: { value: number; label: string };
  loggingViewMode: EnumLoggingViews;

  segmentations: SegmentationResult[];
  isShowSegmentation: boolean;
  isShowPercentageRecovery: boolean;
  getLoggingInfoStatus?: RequestState;
  calculateClickedDepth: EnumCalculateClickedDepth;
  imageGap: number;
  selectedSuite?: string;
  refetchLoggingView: boolean;
  skipCount: number;
  displayColumn: {
    fontSize: number;
    color: string;
    strokeWidth: number;
    lineColor: string;
    showText: boolean;
  };
  offsetY?: number;
  targetDepth?: number;
  loggingSuiteMode: "Geology" | "Geotech" | null;
  openModalGeotechData: boolean;
  imageCropId?: number;
  x?: number;
  depth?: number;
  loggingSuiteId?: number;
  geotechData: any[];
  geotechDataToEdit: any;
  refetchGeotechData: boolean;
  startPointLineInterval: {
    x: number;
    rowIndex: number;
  } | null;
  measurePointsInterval: {
    start?: { x: number; depth: number; imageCropId?: string };
    end?: { x: number; depth: number; imageCropId?: string };
  };
  measureXPointsInterval: {
    start?: { x: number; depth: number; imageCropId?: string };
    end?: { x: number; depth: number; imageCropId?: string };
  };
  loadingGeotechData: boolean;
  recoveries: RockLineCalculationResult[];
  rqdCalculationResult: RockLineCalculationResult[];
  trayDepths: Array<{
    drillHoleId: number;
    trayNumber: number;
    startDepth: number;
    endDepth: number;
    id: number;
  }>;
  toggleUpdateOCR: boolean;
  selectedRockLineType: RockLineType;
  isSplitLine: boolean;
  isMergeLine: boolean;
  isDeleteLine: boolean;
  isJoinLine: boolean;
  isRemoveRock: boolean;
  selectedLine: any;
  isAddLine: boolean;
  loggingSuiteSelected: any;
  geotechSelected: any;
  segmentationsDetail: SegmentationResult[];

  // Logging bar data state
  loggingBarData: LoggingBarItem[];
  loggingBarSuite?: string;
  geologySuiteBar?: any;

  // Pixel color picker mode
  isPixelColorPickerEnabled: boolean;
  rockGroupDetail: any;

  // Rock line length display
  isShowRockLineLength: boolean;

  // Refetch data entry
  fetchDataEntry: boolean;
}

export const {
  updateDisplayColumn,
  updateOffsetY,
  initLogging,
  updateCurrentDepth,
  updateReviewDepthFrom,
  updateSelectedDrilhole,
  updateReviewDepthTo,
  addLoggingLine,
  updateLoggingLine,
  saveLoggingLine,
  saveLoggingData,
  updateLoggingContextMenu,
  updatePreviewImageUrl,
  setImageRows,
  updatePreviousLoggingDepth,
  setImageHyperRows,
  updateExtraViews,
  updateLoggingViewMode,
  updateSegmentsResult,
  updateIsShowSegmentation,
  updateIsShowPercentageRecovery,
  updateCalculateClickedDepth,

  updateImageGap,
  updateSelectedSuite,
  updateRefetchLoggingView,
  updateSkipCount,
  clearLoggingData,
  updateHasShownMeasureInstructions,
  updateLoggingSuiteMode,
  updateOpenModalGeotechData,
  updateCalculateRecoveryBySelectPoint,
  updateLoggingSuiteId,
  updateGeotechData,
  updateGeotechDataToEdit,
  updateRefetchGeotechData,
  updateMeasurePointsInterval,
  updateMeasureXPointsInterval,
  updateLoadingGeotechData,
  updateRecoveries,
  updateTrayDepths,
  updateToggleUpdateOCR,
  updateIsSplitLine,
  updateIsMergeLine,
  updateIsDeleteLine,
  updateIsJoinLine,
  updateIsAddLine,
  updateSelectedLine,
  updateSelectedRockLineType,
  updateLoggingSuiteSelected,
  updateGeotechSelected,
  updateAllLoggingsTable,
  updateSegmentsDetailResult,
  resetInfiniteScrollState,
  updateInfiniteScrollState,
  updateExtraviewImageType,
  updateExtraviewImageSubtype,

  // Main image type and subtype actions
  updateSelectedImageType,
  updateSelectedImageSubtype,
  updateAvailableImageSubtypes,
  updateLegend,
  updateRqdCalculationResult,

  // Logging bar actions
  updateLoggingBarData,
  updateLoggingBarSuite,
  clearLoggingBarData,
  updateGeologySuiteBar,

  // Pixel color picker action
  togglePixelColorPicker,
  updateRockGroupDetail,

  // Rock line length action
  toggleRockLineLength,
  updateIsRemoveRock,

  // Refetch data entry
  updateFetchDataEntry,
} = loggingSlice.actions;
