/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable max-depth */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable complexity */
"use client";
import {
  DeleteOutlined,
  LinkOutlined,
  LoadingOutlined,
  MergeOutlined,
  MinusOutlined,
  PlusOutlined,
  SplitCellsOutlined,
} from "@ant-design/icons";
import { IconPentagon, IconRuler } from "@tabler/icons-react";
import { Dropdown, Modal, Switch, Tooltip } from "antd";
import { Layer as KonvaLayer } from "konva/lib/Layer";
import { KonvaEventObject } from "konva/lib/Node";
import { Stage as KonvaStage } from "konva/lib/Stage";
import { useSearchParams } from "next/navigation";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { CiShare2 } from "react-icons/ci";
import { FaPlus } from "react-icons/fa6";
import { GrPowerReset } from "react-icons/gr";
import { LuRepeat1 } from "react-icons/lu";
import { MdOutlineViewList } from "react-icons/md";
import { SlCalculator } from "react-icons/sl";
import { TbNumber123 } from "react-icons/tb";
import { Group, Layer, Line, Rect, Stage } from "react-konva";
import { toast } from "react-toastify";
import { AutoSizer } from "react-virtualized";

import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import dhCalculationRequest from "@/modules/dh-calculation/api/dh-calculation.api";
import { useGetRockGroup } from "@/modules/rock-groups/hooks/useGetRockGroups";

import { RiTextBlock } from "react-icons/ri";
import dataEntryRequest from "../api/data-entry.api";
import recoveryRequest from "../api/recovery.api";
import {
  ClickDepthData,
  LoggingViewData,
  LoggingViewStack,
} from "../model/dtos/logging.config";
import { EnumLoggingViewStack } from "../model/enum/logging.enum";
import {
  togglePixelColorPicker,
  toggleRockLineLength,
  updateHasShownMeasureInstructions,
  updateImageGap,
  updateIsAddLine,
  updateIsDeleteLine,
  updateIsJoinLine,
  updateIsMergeLine,
  updateIsRemoveRock,
  updateIsShowPercentageRecovery,
  updateIsShowSegmentation,
  updateIsSplitLine,
  updateLoggingContextMenu,
  updateRecoveries,
  updateRockGroupDetail,
  updateRqdCalculationResult,
  updateTrayDepths,
} from "../redux/loggingSlice";
import {
  ApiError,
  ModalState,
  OCRResultItem,
  RockLineCalculationResult,
  RockLineType,
} from "../types/logging.types";
import AddOcrModal from "./add-ocr-modal";
import { ImageRowLogging } from "./image-row-logging";
import { ModalEditDepth } from "./modal-edit-depth";
import ModalEntryData from "./modal-entry-data";
import { PixelColorPicker } from "./pixel-color-picker";

interface LoggingStageProps {
  loggingViewStacks: LoggingViewStack<LoggingViewData>[];
  directOCRdata: OCRResultItem[];
  onChangeText: (data: OCRResultItem, value: string) => void;
  onEnterChangeText: (data: OCRResultItem, value: string) => void;
  onDblClickText: (e: KonvaEventObject<MouseEvent>) => void;
  onChange: (e: KonvaEventObject<MouseEvent>) => void;
  directOCRdataRaw: OCRResultItem[];
  image: {
    id: string | number;
    [key: string]: any;
  };
  setDirectOCRdata: (data: OCRResultItem[]) => void;
  setIsOpenModalOCRList: (isOpen: boolean) => void;
  refreshImageData: () => void;
  fetchRecoveries: () => void;
  onDeleteOcr: (id: string) => void;
  refreshLoggingBarData?: () => void;
  refetchDataEntry?: (isSaveLine?: boolean, drillholeId?: number) => void;
  modalStateEntryData: any;
  setModalStateEntryData: (state: any) => void;
  isPixelColorPickerEnabled?: boolean;
}
const AutoSizerTmp: any = AutoSizer;

function LoggingStage({
  loggingViewStacks,
  directOCRdata,
  onChange,
  onChangeText,
  onDblClickText,
  onEnterChangeText,
  image,
  setDirectOCRdata,
  setIsOpenModalOCRList,
  refreshImageData,
  onDeleteOcr,
  refreshLoggingBarData,
  refetchDataEntry,
  modalStateEntryData,
  setModalStateEntryData,
  isPixelColorPickerEnabled,
}: LoggingStageProps) {
  const searchParams = useSearchParams();
  const queries: any = {};
  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId,
  );
  const globalProspectId = useAppSelector(
    (state) => state.user?.userInfo?.prospectId,
  );

  const selectedRockLineType = useAppSelector(
    (state) => state.logging.selectedRockLineType,
  );

  const isShowRockLineLength = useAppSelector(
    (state) => state.logging.isShowRockLineLength,
  );

  const scaleBy = 1.1;
  const dispatch = useAppDispatch();
  const contextMenu = useAppSelector((state) => state.logging.contextMenu);
  const imageDepthFrom = useAppSelector((state) => state.logging.depthFrom);
  const imageDepthTo = useAppSelector((state) => state.logging.depthTo);
  const imageRows = useAppSelector((state) => state.logging.imageRows);
  const loggingBarSuiteId = useAppSelector(
    (state) => state.logging.loggingBarSuite,
  );

  // Touch zoom state
  const [lastCenter, setLastCenter] = useState<{ x: number; y: number } | null>(
    null,
  );
  const [lastDist, setLastDist] = useState<number>(0);
  const [isZooming, setIsZooming] = useState(false);
  const touchStartTime = useRef<number>(0);
  const initialTouchCenterRef = useRef<{ x: number; y: number } | null>(null);

  // Helper functions for touch handling
  const getDistance = (
    p1: { x: number; y: number },
    p2: { x: number; y: number },
  ) => {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  };

  const getCenter = (
    p1: { x: number; y: number },
    p2: { x: number; y: number },
  ) => {
    return {
      x: (p1.x + p2.x) / 2,
      y: (p1.y + p2.y) / 2,
    };
  };

  const handleTouchStart = (e: KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;
    touchStartTime.current = Date.now();

    if (touches.length === 2) {
      setIsZooming(true);
      const stage = stageRef.current;
      if (!stage) {
        return;
      }

      const p1 = { x: touches[0].clientX, y: touches[0].clientY };
      const p2 = { x: touches[1].clientX, y: touches[1].clientY };

      const center = getCenter(p1, p2);
      const dist = getDistance(p1, p2);

      const stageBox = stage.container().getBoundingClientRect();
      const stagePoint = {
        x: center.x - stageBox.left,
        y: center.y - stageBox.top,
      };

      const oldScale = stage.scaleX();
      const centerPointTo = {
        x: (stagePoint.x - stage.x()) / oldScale,
        y: (stagePoint.y - stage.y()) / oldScale,
      };

      initialTouchCenterRef.current = centerPointTo;
      setLastCenter(center);
      setLastDist(dist);
    } else if (touches.length === 1) {
      setIsZooming(false);
    }
  };

  const handleTouchMove = (e: KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;

    if (touches.length !== 2) {
      return;
    }

    const stage = stageRef.current;
    if (!stage || !lastCenter || !lastDist || !initialTouchCenterRef.current) {
      return;
    }

    const p1 = { x: touches[0].clientX, y: touches[0].clientY };
    const p2 = { x: touches[1].clientX, y: touches[1].clientY };

    const dist = getDistance(p1, p2);
    const currentScale = stage.scaleX();
    const currentCenter = getCenter(p1, p2);
    const stageBox = stage.container().getBoundingClientRect();
    const stagePoint = {
      x: currentCenter.x - stageBox.left,
      y: currentCenter.y - stageBox.top,
    };

    const scaleFactor = Math.pow(dist / lastDist, 1.2);
    const zoomSpeed = 1.03;
    const targetScale = Math.min(
      Math.max(currentScale * scaleFactor * zoomSpeed, 0.03),
      10,
    );

    stage.scale({ x: targetScale, y: targetScale });

    const newPos = {
      x: stagePoint.x - initialTouchCenterRef.current.x * targetScale,
      y: stagePoint.y - initialTouchCenterRef.current.y * targetScale,
    };

    stage.position(newPos);
    stage.batchDraw();

    setLastDist(dist);
    setLastCenter(currentCenter);
    setIsZooming(true);
  };

  const handleTouchEnd = (e: KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    if (e.evt.touches.length < 2) {
      initialTouchCenterRef.current = null;
      setLastCenter(null);
      setLastDist(0);
    }

    if (e.evt.touches.length === 0) {
      setIsZooming(false);
      touchStartTime.current = 0;
    }
  };

  const isShowSegmentation = useAppSelector(
    (state) => state.logging.isShowSegmentation,
  );
  const isSplitLine = useAppSelector((state) => state.logging.isSplitLine);
  const isMergeLine = useAppSelector((state) => state.logging.isMergeLine);
  const isAddLine = useAppSelector((state) => state.logging.isAddLine);
  const isDeleteLine = useAppSelector((state) => state.logging.isDeleteLine);
  const isJoinLine = useAppSelector((state) => state.logging.isJoinLine);
  const isRemoveRock = useAppSelector((state) => state.logging.isRemoveRock);
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole,
  );
  const hasShownMeasureInstructions = useAppSelector(
    (state) => state.logging.hasShownMeasureInstructions,
  );
  const stageRef = useRef<KonvaStage>(null);
  const layerRef = useRef<KonvaLayer>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const initialScale = useRef<number>();

  const [isShowOCR, setIsShowOCR] = useState(false);

  const handleClickStage = (event: KonvaEventObject<MouseEvent>) => {
    if (event.target.name() !== "loggingImageRow" && contextMenu.visible) {
      dispatch(
        updateLoggingContextMenu({
          visible: false,
          isRightClick: false,
        }),
      );
    }
  };

  const handleMeasurePoint = (point: {
    x: number;
    depth: number;
    rowIndex: number;
  }) => {
    if (!measurePoints.start) {
      setMeasurePoints({
        start: point,
      });
      setStartPointLine({ x: point.x, rowIndex: point.rowIndex });
      toast.info("First point set. Click to set end point.");
    } else {
      setMeasurePoints((prev) => ({
        ...prev,
        end: point,
      }));
      setStartPointLine(null);
      setIsMeasuring(false);
      setIsMeasureModalVisible(true);
    }
  };

  const handleOnWheelStage = (e: KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();
    const stage = stageRef.current;
    const layer = layerRef.current;
    if (!layer || !stage) {
      return;
    }
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) {
      return;
    }
    const mousePointTo = {
      x: pointer.x / oldScale - stage.x() / oldScale,
      y: pointer.y / oldScale - stage.y() / oldScale,
    };
    let direction = e.evt.deltaY > 0 ? -1 : 1;
    if (e.evt.ctrlKey) {
      direction = -direction;
    }
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;
    if (newScale < 0.03) {
      return;
    }
    stage.scale({ x: newScale, y: newScale });
    stage.scale({ x: newScale, y: newScale });
    const newPos = {
      x: -(mousePointTo.x - pointer.x / newScale) * newScale,
      y: -(mousePointTo.y - pointer.y / newScale) * newScale,
    };
    stage.position(newPos);
    stage.batchDraw();
  };

  const considerWidth = useMemo(() => {
    const maxWidth = Math.max(
      ...loggingViewStacks
        .filter((stack) => stack.type === EnumLoggingViewStack.Image)
        .map((stack) => stack.data.width || 0),
    );
    return maxWidth;
  }, [loggingViewStacks]);

  useEffect(() => {
    // Calculate initial scale of stage base on image width and screen width
    if (containerRef.current && considerWidth) {
      const containerWidth = containerRef.current.clientWidth;
      // Calculate scale but ensure it's not too small
      let scale = (containerWidth - 200) / considerWidth;
      scale = Math.max(scale, 0.2); // Ensure a minimum scale
      stageRef.current?.scale({ x: scale, y: scale });
      stageRef.current?.position({ x: 60, y: 0 }); // Set a consistent initial position
      stageRef.current?.batchDraw();

      initialScale.current = scale;
    }
  }, []);

  const resetRoom = () => {
    stageRef.current?.scale({
      x: initialScale.current ?? 1.0, // Using 1.0 as a default scale
      y: initialScale.current ?? 1.0,
    });
    stageRef.current?.position({ x: 60, y: 0 });
    stageRef.current?.batchDraw();
  };

  // This use for get clicked depth based on result of OCR
  const clickedDepthData: ClickDepthData[] = useMemo(() => {
    const standardImages = loggingViewStacks.filter(
      (view) => view.type === EnumLoggingViewStack.Image,
    );

    const depthDatas: any = [];
    const considerDepthDatas = [
      {
        rowIndex: 0,
        text: imageDepthFrom,
        x: 0,
      },
      ...directOCRdata,
      {
        rowIndex: standardImages.length,
        text: imageDepthTo,
        x: standardImages[standardImages.length - 1]?.data?.width ?? 0,
      },
    ];

    considerDepthDatas.forEach((ocr) => {
      // Get current image data safely with optional chaining and fallback
      const currentImage = standardImages[ocr.rowIndex];
      const relativeStartX = currentImage?.data?.relativeStartX ?? 0;

      depthDatas.push({
        relativeX: relativeStartX + (ocr.x ?? 0),
        depth: ocr.text || "",
      });
    });
    return depthDatas;
  }, [loggingViewStacks, directOCRdata]);
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    data: undefined,
  });

  const [stateAddOcr, setStateAddOcr] = useState<{
    isOpen: boolean;
    x: number;
    y: number;
    rowIndex: number;
  }>({
    isOpen: false,
    x: 0,
    y: 0,
    rowIndex: 0,
  });

  const [addOCR, setAddOCR] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isCalculatingLoggingBar, setIsCalculatingLoggingBar] = useState(false);
  const recoveries = useAppSelector((state) => state.logging.recoveries);
  const rqdCalculationResult = useAppSelector(
    (state) => state.logging.rqdCalculationResult,
  );
  const [isMeasuring, setIsMeasuring] = useState(false);
  const [measurePoints, setMeasurePoints] = useState<{
    start?: { x: number; depth: number };
    end?: { x: number; depth: number };
  }>({});
  const [isMeasureModalVisible, setIsMeasureModalVisible] = useState(false);
  const [startPointLine, setStartPointLine] = useState<{
    x: number;
    rowIndex: number;
  } | null>(null);
  const [stageHeight, setStageHeight] = useState<number>(0);

  // Pixel color picker state
  const [colorInfo, setColorInfo] = useState<{
    rgb: string;
    hex: string;
    x: number;
    y: number;
  } | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // State for entry data modal
  const [entryDataModal, setEntryDataModal] = useState<{
    isOpen: boolean;
    detail?: any;
    type: "create" | "update" | "delete" | "view" | "update-bar";
  }>({
    isOpen: false,
    detail: undefined,
    type: "create",
  });

  const considerRockLineCalculationResult = useMemo(() => {
    if (selectedRockLineType === RockLineType.RQD) {
      return (rqdCalculationResult ?? [])?.filter(
        (rockLineCalculationResult) =>
          (typeof imageDepthFrom === "number" &&
            typeof imageDepthTo === "number" &&
            // Recovery start point intersects with image depth range
            ((rockLineCalculationResult.ocrValueFrom >= imageDepthFrom &&
              rockLineCalculationResult.ocrValueFrom < imageDepthTo) ||
              // Recovery end point intersects with image depth range
              (rockLineCalculationResult.ocrValueTo > imageDepthFrom &&
                rockLineCalculationResult.ocrValueTo <= imageDepthTo) ||
              // Recovery encompasses image depth range
              (rockLineCalculationResult.ocrValueFrom <= imageDepthFrom &&
                rockLineCalculationResult.ocrValueTo >= imageDepthTo))) ||
          directOCRdata.find(
            (ocr) =>
              Number(ocr.text) === rockLineCalculationResult.ocrValueFrom ||
              Number(ocr.text) === rockLineCalculationResult.ocrValueTo,
          ),
      );
    }

    return (recoveries ?? [])?.filter(
      (rockLineCalculationResult) =>
        (typeof imageDepthFrom === "number" &&
          typeof imageDepthTo === "number" &&
          // Recovery start point intersects with image depth range
          ((rockLineCalculationResult.ocrValueFrom >= imageDepthFrom &&
            rockLineCalculationResult.ocrValueFrom < imageDepthTo) ||
            // Recovery end point intersects with image depth range
            (rockLineCalculationResult.ocrValueTo > imageDepthFrom &&
              rockLineCalculationResult.ocrValueTo <= imageDepthTo) ||
            // Recovery encompasses image depth range
            (rockLineCalculationResult.ocrValueFrom <= imageDepthFrom &&
              rockLineCalculationResult.ocrValueTo >= imageDepthTo))) ||
        directOCRdata.find(
          (ocr) =>
            Number(ocr.text) === rockLineCalculationResult.ocrValueFrom ||
            Number(ocr.text) === rockLineCalculationResult.ocrValueTo,
        ),
    );
  }, [
    recoveries,
    imageDepthFrom,
    imageDepthTo,
    directOCRdata,
    selectedRockLineType,
  ]);

  const rockLineCalculationResultWithRowDisplays: (RockLineCalculationResult & {
    rowDisplays: number[];
  })[] = useMemo(() => {
    const imageRowsWithIndex = imageRows.map((imageRow, rowIndex) => {
      return {
        ...imageRow,
        rowIndex: rowIndex,
      };
    });

    return (considerRockLineCalculationResult ?? []).map(
      (rockLineCalculationResult) => {
        const rowDisplays = imageRowsWithIndex
          .filter((imageRowWithIndex) => {
            if (rockLineCalculationResult.ocrValueTo < imageDepthFrom) {
              return false;
            }
            if (rockLineCalculationResult.ocrValueFrom > imageDepthTo) {
              return false;
            }

            const isOCRFromVisible = directOCRdata.find(
              (directOcr) =>
                Number(directOcr.text) ===
                rockLineCalculationResult.ocrValueFrom,
            );
            const isOCRToVisible = directOCRdata.find(
              (directOcr) =>
                Number(directOcr.text) === rockLineCalculationResult.ocrValueTo,
            );

            if (
              Number(rockLineCalculationResult.ocrValueFrom.toFixed(2)) ===
              Number(imageDepthTo.toFixed(2))
            ) {
              return false;
            }

            if (
              !isOCRFromVisible &&
              !isOCRToVisible &&
              rockLineCalculationResult.ocrValueTo < imageDepthTo
            ) {
              return false;
            }

            if (!isOCRFromVisible && !isOCRToVisible) {
              return true;
            }

            if (isOCRFromVisible && !isOCRToVisible) {
              return (
                imageRowWithIndex.rowIndex >=
                  rockLineCalculationResult.fromRowIndex &&
                imageRowWithIndex.rowIndex >= isOCRFromVisible.rowIndex
              );
            }

            if (!isOCRFromVisible && isOCRToVisible) {
              return (
                imageRowWithIndex.rowIndex <=
                  rockLineCalculationResult.toRowIndex &&
                imageRowWithIndex.rowIndex <= isOCRToVisible.rowIndex
              );
            }

            return (
              imageRowWithIndex.rowIndex >=
                rockLineCalculationResult.fromRowIndex &&
              imageRowWithIndex.rowIndex <= rockLineCalculationResult.toRowIndex
            );
          })
          .map((imageRowWithIndex) => imageRowWithIndex.rowIndex);
        return {
          ...rockLineCalculationResult,
          rowDisplays: rowDisplays,
        };
      },
    );
  }, [
    considerRockLineCalculationResult,
    imageRows,
    directOCRdata,
    imageDepthFrom,
    imageDepthTo,
    selectedRockLineType,
  ]);

  const drillholeId = useAppSelector(
    (state) => state.logging.selectedDrillHole,
  );
  const selectedImageSubtypeId = useAppSelector(
    (state) => state.logging.selectedImageSubtypeId,
  );
  const selectedImageTypeId = useAppSelector(
    (state) => state.logging.selectedImageTypeId,
  );

  const handleCalculateRecoveries = async () => {
    if (!selectedDrillhole?.value) {
      toast.error("No drillhole selected");
      return;
    }

    setIsCalculating(true);
    setIsModalVisible(false);

    try {
      const response = await dhCalculationRequest.executeCalculationRqd({
        drillholeId: Number(drillholeId?.value),
      });
      if (response.state !== RequestState.success) {
        toast.error(response.message || "Failed to calculate recoveries");
        return;
      }
      const result = await recoveryRequest.calculateRecovery(
        Number(selectedDrillhole.value),
      );
      if (result.state === RequestState.success) {
        // Calculate tray depth
        const trayDepthResult = await dhCalculationRequest.calculateTrayDepth({
          drillHoleId: Number(drillholeId?.value),
        });

        if (trayDepthResult.state !== RequestState.success) {
          toast.warning(
            "Tray depth calculation failed, but recoveries were calculated",
          );
        } else {
          // Fetch updated tray depth data after calculation
          const trayDepthsResult = await recoveryRequest.getTrayDepthResult({
            drillHoleId: Number(selectedDrillhole.value),
            maxResultCount: 1000,
            skipCount: 0,
          });

          if (
            trayDepthsResult.state === RequestState.success &&
            trayDepthsResult.data?.items
          ) {
            dispatch(updateTrayDepths(trayDepthsResult.data.items));
          }
        }

        // Get recoveries after calculation
        const recoveriesResult = await recoveryRequest.getRecoveryByDrillHole({
          drillHoleId: Number(selectedDrillhole.value),
        });
        if (
          recoveriesResult.state === RequestState.success &&
          recoveriesResult.data?.items
        ) {
          dispatch(updateRecoveries(recoveriesResult.data.items));
          toast.success("Recoveries calculated successfully");
          // Force refresh image data to update resultText display
          if (refreshImageData) {
            // Add a small delay to ensure Redux state is updated
            setTimeout(() => {
              refreshImageData();
            }, 100);
          }
        }
      } else {
        // Show detailed error message if available
        const errorMessage = result.message || "Failed to calculate recoveries";

        toast.error(errorMessage);
      }
    } catch (error) {
      const apiError = error as ApiError;
      const errorMessage =
        apiError.response?.data?.error?.message ??
        apiError.message ??
        "An error occurred while calculating recoveries";

      toast.error(errorMessage);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleCalculateLoggingBar = async () => {
    if (!selectedDrillhole?.value) {
      toast.error("No drillhole selected");
      return;
    }

    if (!loggingBarSuiteId) {
      toast.error("No geology suite selected");
      return;
    }

    if (!selectedImageSubtypeId) {
      toast.error("No image subtype selected");
      return;
    }

    setIsCalculatingLoggingBar(true);

    try {
      const response = await dataEntryRequest.calculateLoggingBar({
        geologySuiteId: Number(loggingBarSuiteId),
        drillHoleId: Number(selectedDrillhole.value),
        imageSubtypeId: Number(selectedImageSubtypeId),
        imageTypeId: Number(selectedImageTypeId),
      });

      if (response.state === RequestState.success) {
        toast.success("Logging bar recalculated successfully");
        // Refresh the image data to reflect the changes
        refreshImageData();
        // Refresh logging bar data if geology suite is selected
        if (refreshLoggingBarData) {
          refreshLoggingBarData();
        }
      } else {
        const errorMessage =
          response.message || "Failed to recalculate logging bar";
        toast.error(errorMessage);
      }
    } catch (error) {
      const apiError = error as ApiError;
      const errorMessage =
        apiError.response?.data?.error?.message ??
        apiError.message ??
        "An error occurred while recalculating logging bar";

      toast.error(errorMessage);
    } finally {
      setIsCalculatingLoggingBar(false);
    }
  };

  useEffect(() => {
    if (!isShowSegmentation) {
      dispatch(updateImageGap(200));
      return;
    }

    if (isShowSegmentation && considerRockLineCalculationResult.length > 0) {
      dispatch(updateImageGap(350));
    }
  }, [considerRockLineCalculationResult, isShowSegmentation]);
  const projectDetail = useAppSelector((state) => state.project.detail);
  const rockGroupId = projectDetail?.rockGroup?.id;
  const { data: rockGroupDetail, request: requestGetRockGroup } =
    useGetRockGroup();
  useEffect(() => {
    if (rockGroupId) {
      requestGetRockGroup(rockGroupId, (res) => {
        dispatch(updateRockGroupDetail(res));
      });
    }
  }, [rockGroupId]);
  const { legend } = useAppSelector((state) => state.logging);

  useEffect(() => {
    if (drillholeId?.value) {
      dhCalculationRequest
        .getRqdCalculationResultByDrillHole({
          drillHoleId: Number(drillholeId?.value),
          maxResultCount: 1000,
          skipCount: 0,
        })
        .then((res) => {
          if (res.state === RequestState.success) {
            dispatch(updateRqdCalculationResult(res.data?.items));
          }
        });
    }
  }, [drillholeId]);

  // Initialize stage height when container is available
  useEffect(() => {
    if (containerRef.current && !stageHeight) {
      const containerHeight = containerRef.current.clientHeight;
      setStageHeight(Math.max(containerHeight - 20, 300));
    }
  }, [stageHeight]);

  // Update stage height when container size changes
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const containerHeight = containerRef.current.clientHeight;
        setStageHeight(Math.max(containerHeight - 20, 300));
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return (
    <div
      className="w-full h-[calc(100vh_-_166px)] min-h-[400px] relative"
      ref={containerRef}
    >
      <div className="absolute z-10 top-0 left-0 flex select-none flex-col ">
        <Tooltip title="Reset zoom" placement="left">
          <div
            className="p-2 flex items-center justify-center border border-gray-300 hover:bg-gray-100 bg-white rounded-tr-md hover:cursor-pointer"
            onClick={resetRoom}
          >
            <GrPowerReset style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        <Tooltip title="Open block list" placement="left">
          <div
            className="p-2 mt-3 flex items-center justify-center border border-b-0 border-gray-300 hover:bg-gray-100 bg-white hover:cursor-pointer"
            onClick={() => setIsOpenModalOCRList(true)}
          >
            <MdOutlineViewList style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        <Tooltip
          title={`${isShowOCR ? "Hide" : "Display"} block depths`}
          placement="left"
        >
          <div
            className={`p-2 flex items-center justify-center border border-gray-300 hover:cursor-pointer  ${
              isShowOCR
                ? "bg-primary text-white border-primary"
                : "bg-white hover:bg-gray-100"
            }`}
            onClick={() => setIsShowOCR(!isShowOCR)}
          >
            <RiTextBlock style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        {isShowOCR && (
          <Tooltip title={"Add depth"} placement="left">
            <div
              onClick={() => setAddOCR(!addOCR)}
              className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                addOCR
                  ? "bg-primary text-white border-primary border-t border-t-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <FaPlus style={{ width: 20, height: 20 }} />
            </div>
          </Tooltip>
        )}
        <Tooltip title={"Recalculate Geology Bars"} placement="left">
          <div
            onClick={handleCalculateLoggingBar}
            className={`p-2 flex items-center justify-center border border-gray-300 border-t-0 rounded-br-md hover:cursor-pointer bg-white hover:bg-gray-100`}
          >
            {isCalculatingLoggingBar ? (
              <LoadingOutlined style={{ width: 20, height: 20 }} />
            ) : (
              <LuRepeat1 style={{ width: 20, height: 20 }} />
            )}
          </div>
        </Tooltip>

        <Tooltip
          title={`${isShowSegmentation ? "Hide" : "Display "} rock segments`}
          placement="left"
        >
          <div
            className={`p-2 mt-3 flex items-center justify-center border border-gray-300 hover:cursor-pointer ${
              isShowSegmentation
                ? "bg-primary text-white border-primary border-t border-t-white"
                : "bg-white hover:bg-gray-100"
            }`}
            onClick={() => {
              dispatch(updateIsShowSegmentation(!isShowSegmentation));
              dispatch(updateIsShowPercentageRecovery(!isShowSegmentation));

              // Reset segmentation function state
              dispatch(updateIsSplitLine(false));
              dispatch(updateIsMergeLine(false));
              dispatch(updateIsAddLine(false));
              dispatch(updateIsDeleteLine(false));
            }}
          >
            <IconPentagon style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        {isShowSegmentation && (
          <Tooltip
            title={`${isShowRockLineLength ? "Hide" : "Show"} rock line length`}
            placement="left"
          >
            <div
              onClick={() => {
                dispatch(toggleRockLineLength());
              }}
              className={`p-2 flex items-center justify-center border-gray-300 border hover:cursor-pointer ${
                isShowRockLineLength
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <TbNumber123 style={{ width: 20, height: 20 }} />
            </div>
          </Tooltip>
        )}

        {isShowSegmentation && (
          <>
            <Tooltip
              title={"Calculate Recovery, RQD & Tray Depth"}
              placement="left"
            >
              <div
                onClick={() => setIsModalVisible(true)}
                className={`p-2 flex items-center justify-center border border-gray-300 border-t-0 hover:cursor-pointer bg-white hover:bg-gray-100`}
              >
                {isCalculating ? (
                  <LoadingOutlined style={{ width: 20, height: 20 }} />
                ) : (
                  <SlCalculator style={{ width: 20, height: 20 }} />
                )}
              </div>
            </Tooltip>
            <Tooltip title={`Split rock line`} placement="left">
              <div
                onClick={() => {
                  dispatch(updateIsSplitLine(!isSplitLine));
                }}
                className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                  isSplitLine
                    ? "bg-primary text-white border-primary border-t border-t-white"
                    : "bg-white hover:bg-gray-100"
                }`}
              >
                <SplitCellsOutlined width={20} />
              </div>
            </Tooltip>
            <Tooltip title={`Add rock line`} placement="left">
              <div
                onClick={() => {
                  dispatch(updateIsAddLine(!isAddLine));
                  // Turn off other modes when enabling add line mode
                }}
                className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                  isAddLine
                    ? "bg-primary text-white border-primary border-t border-t-white"
                    : "bg-white hover:bg-gray-100"
                }`}
              >
                <PlusOutlined />
              </div>
            </Tooltip>
            <Tooltip title={`Merge rock line`} placement="left">
              <div
                onClick={() => {
                  dispatch(updateIsMergeLine(!isMergeLine));
                }}
                className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                  isMergeLine
                    ? "bg-primary text-white border-primary border-t border-t-white"
                    : "bg-white hover:bg-gray-100"
                }`}
              >
                <MergeOutlined width={20} />
              </div>
            </Tooltip>
            <Tooltip title={`Join rock line`} placement="left">
              <div
                onClick={() => {
                  dispatch(updateIsJoinLine(!isJoinLine));
                }}
                className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                  isJoinLine
                    ? "bg-primary text-white border-primary border-t border-t-white"
                    : "bg-white hover:bg-gray-100"
                }`}
              >
                <LinkOutlined width={20} />
              </div>
            </Tooltip>
            <Tooltip title={`Remove rock line`} placement="left">
              <div
                onClick={() => {
                  dispatch(updateIsRemoveRock(!isRemoveRock));
                }}
                className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                  isRemoveRock
                    ? "bg-primary text-white border-primary border-t border-t-white"
                    : "bg-white hover:bg-gray-100"
                }`}
              >
                <MinusOutlined width={20} />
              </div>
            </Tooltip>
            <Tooltip title={`Delete rock line`} placement="left">
              <div
                onClick={() => {
                  dispatch(updateIsDeleteLine(!isDeleteLine));
                }}
                className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                  isDeleteLine
                    ? "bg-primary text-white border-primary border-t border-t-white"
                    : "bg-white hover:bg-gray-100"
                }`}
              >
                <DeleteOutlined width={20} />
              </div>
            </Tooltip>
          </>
        )}

        <Tooltip title={"Measure distance"} placement="left">
          <div
            onClick={() => {
              if (isMeasuring) {
                setIsMeasuring(false);
                setStartPointLine(null);
                setMeasurePoints({});
              } else {
                setIsMeasuring(true);
                setMeasurePoints({});
                setIsMeasureModalVisible(true);
              }
            }}
            className={`p-2 mt-3 flex items-center justify-center border border-gray-300  hover:cursor-pointer ${
              isMeasuring
                ? "bg-primary text-white"
                : "bg-white hover:bg-gray-100"
            }`}
          >
            <IconRuler style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>

        <div>
          <div className="p-2 mt-3 flex items-center justify-center border border-gray-300 hover:cursor-pointer bg-white hover:bg-gray-100">
            <Dropdown
              placement="bottom"
              menu={{
                items: [
                  {
                    label: (
                      <p
                        className="text-sm"
                        onClick={() => {
                          const params = {
                            projectId: queries.projectId ?? globalProjectId,
                            prospectId: queries.prospectId ?? globalProspectId,
                            drillholeId:
                              queries.drillholeId ?? drillholeId?.value,
                            activeKey: "Image",
                            loggingSuiteId: queries.loggingSuiteId,
                            geotechSuiteId: queries.geotechSuiteId,
                            imageTypeId: queries.imageTypeId,
                            imageSubTypeId: queries.imageSubTypeId,
                          };
                          const url = `${
                            window.location.origin
                          }/logging?${new URLSearchParams(params).toString()}`;
                          navigator.clipboard.writeText(url);
                          toast.success("Link copied to clipboard");
                        }}
                      >
                        Link to Logging Image Page
                      </p>
                    ),
                    key: "share",
                  },
                  {
                    label: (
                      <p
                        className="text-sm"
                        onClick={() => {
                          const params = {
                            projectId: queries.projectId ?? globalProjectId,
                            prospectId: queries.prospectId ?? globalProspectId,
                            drillholeId:
                              queries.drillholeId ?? drillholeId?.value,
                            activeKey: "Visualize",
                            loggingSuiteId: queries.loggingSuiteId,
                            geotechSuiteId: queries.geotechSuiteId,
                            imageTypeId: queries.imageTypeId,
                            imageSubTypeId: queries.imageSubTypeId,
                            loggingViewId: queries.loggingViewId,
                            depthInput: queries.depthInput,
                          };
                          const _param: any = {};
                          // Only add params that have values
                          Object.entries(params).forEach(([key, value]) => {
                            if (value) {
                              _param[key] = value;
                            }
                          });
                          const url = `${
                            window.location.origin
                          }/logging?${new URLSearchParams(_param).toString()}`;
                          navigator.clipboard.writeText(url);
                          toast.success("Link copied to clipboard");
                        }}
                      >
                        Link to Logging Visualize Page
                      </p>
                    ),
                    key: "share-visualize",
                  },
                ],
              }}
            >
              <CiShare2 style={{ width: 20, height: 20 }} />
            </Dropdown>
          </div>
        </div>

        <Modal
          title={
            measurePoints.start ? "Measurement Results" : "Start Measuring"
          }
          open={
            isMeasureModalVisible &&
            ((!hasShownMeasureInstructions && isMeasuring) ||
              (!!measurePoints.start && !!measurePoints.end))
          }
          onOk={() => {
            if (!hasShownMeasureInstructions) {
              dispatch(updateHasShownMeasureInstructions(true));
            }
            setIsMeasureModalVisible(false);
          }}
          onCancel={() => {
            if (!hasShownMeasureInstructions) {
              dispatch(updateHasShownMeasureInstructions(true));
            }
            setIsMeasureModalVisible(false);
          }}
          okText={measurePoints.start ? "Close" : "Got it"}
          cancelButtonProps={{ style: { display: "none" } }}
          maskClosable={false}
        >
          <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
            {!measurePoints.start ? (
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <IconRuler className="text-primary" />
                  <span className="text-lg font-medium text-gray-800">
                    Instructions
                  </span>
                </div>
                <p className="text-gray-600">
                  Click on any image to set your starting point for measurement.
                </p>
                <div className="mt-4 bg-blue-50 p-3 rounded border border-blue-200">
                  <p className="text-sm text-blue-700">
                    Tip: The measurement will show the distance between two
                    points you select.
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-gray-600">
                Start Point: {measurePoints.start.depth.toFixed(2)} m
              </div>
            )}

            {measurePoints.end && (
              <div className="text-gray-600">
                End Point: {measurePoints.end.depth.toFixed(2)} m
              </div>
            )}

            {measurePoints.start && measurePoints.end && (
              <div className="p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-sm font-bold text-blue-900">
                  Distance:{" "}
                  {Math.abs(
                    measurePoints.end.depth - measurePoints.start.depth,
                  ).toFixed(2)}
                  m
                </div>
              </div>
            )}
          </div>
        </Modal>

        <Modal
          title="Confirm Calculations"
          open={isModalVisible}
          onOk={handleCalculateRecoveries}
          onCancel={() => setIsModalVisible(false)}
        >
          <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-base font-medium text-gray-800">
              Please confirm that you want to calculate recoveries, RQD, and
              tray depth for this drillhole.
            </p>
            <div>
              <p className="text-base text-gray-700 font-medium mb-2">
                Before proceeding, ensure that:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li className="text-gray-600 hover:text-primary transition-colors">
                  OCR values are correctly set
                </li>
                <li className="text-gray-600 hover:text-primary transition-colors">
                  Segmentation is properly defined
                </li>
                <li className="text-gray-600 hover:text-primary transition-colors">
                  Images are properly cropped
                </li>
              </ul>
            </div>
            <div className="bg-yellow-50 p-3 rounded border border-yellow-200">
              <p className="text-sm text-yellow-700">
                Note: This process cannot be undone. Please review carefully
                before proceeding.
              </p>
            </div>
          </div>
        </Modal>

        {/* <Tooltip title={"Save"} placement="left">
          <div
            onClick={handleSaveOCR}
            className="p-2 flex items-center justify-center border-l border-r border-b border-gray-300 rounded-br-md hover:cursor-pointer bg-white hover:bg-gray-100"
          >
            {loadingSaveOCR ? (
              <LoadingOutlined style={{ width: 20, height: 20 }} />
            ) : (
              <RiSave3Line style={{ width: 20, height: 20 }} />
            )}
          </div>
        </Tooltip> */}
      </div>

      {modalState.isOpen && (
        <ModalEditDepth
          modalState={modalState}
          onEnterChangeText={onEnterChangeText}
          setModalState={setModalState}
          setDirectOCRdata={setDirectOCRdata}
          onDeleteOcr={onDeleteOcr}
          image={image}
          directOCRdata={directOCRdata}
        />
      )}
      {stateAddOcr.isOpen && (
        <AddOcrModal
          stateAddOcr={stateAddOcr}
          setStateAddOcr={setStateAddOcr}
          setDirectOCRdata={setDirectOCRdata}
          directOCRdata={directOCRdata}
          image={image}
        />
      )}

      {/* Entry Data Modal */}
      <ModalEntryData
        modalState={entryDataModal}
        setModalState={setEntryDataModal}
        refetchDataEntry={(isSaveLine = true, drillholeId) => {
          // Refresh data after successful edit
          if (refreshImageData) {
            refreshImageData();
          }
          // Call the parent refetchDataEntry if provided
          if (refetchDataEntry) {
            refetchDataEntry(isSaveLine, drillholeId);
          }
        }}
      />
      <AutoSizerTmp>
        {({ width, height }) => {
          const calculatedHeight = Math.max(height - 20, 300);
          // Update stage height when it changes
          if (stageHeight !== calculatedHeight) {
            setStageHeight(calculatedHeight);
          }

          return (
            <Stage
              draggable={!isZooming}
              x={60}
              y={0}
              onWheel={handleOnWheelStage}
              ref={stageRef}
              width={legend ? width - 160 : width}
              height={calculatedHeight}
              onClick={handleClickStage}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              onMouseMove={
                isPixelColorPickerEnabled
                  ? (e) => {
                      if (!isPixelColorPickerEnabled || !stageRef.current) {
                        return;
                      }

                      const stage = stageRef.current;
                      const pointer = stage.getPointerPosition();

                      if (!pointer) {
                        return;
                      }

                      try {
                        const { x, y } = pointer;

                        // Get the canvas from the stage and read pixel data
                        const canvas = stage.toCanvas();
                        const ctx = canvas.getContext("2d");

                        if (ctx) {
                          try {
                            // Ensure coordinates are within canvas bounds
                            const canvasWidth = canvas.width;
                            const canvasHeight = canvas.height;

                            if (
                              x >= 0 &&
                              x < canvasWidth &&
                              y >= 0 &&
                              y < canvasHeight
                            ) {
                              const pixel = ctx.getImageData(
                                Math.round(x),
                                Math.round(y),
                                1,
                                1,
                              ).data;
                              const [r, g, b, a] = pixel;

                              // Only show color if pixel is not transparent
                              if (a > 0) {
                                const rgb = `rgb(${r}, ${g}, ${b})`;
                                const hex = `#${[r, g, b]
                                  .map((v) => v.toString(16).padStart(2, "0"))
                                  .join("")}`;

                                setColorInfo({
                                  rgb,
                                  hex,
                                  x: Math.round(x),
                                  y: Math.round(y),
                                });
                                setTooltipPosition({
                                  x: e.evt.clientX + 10,
                                  y: e.evt.clientY - 10,
                                });
                                setShowTooltip(true);
                              } else {
                                setShowTooltip(false);
                              }
                            } else {
                              setShowTooltip(false);
                            }
                          } catch {
                            setShowTooltip(false);
                          }
                        } else {
                          setShowTooltip(false);
                        }
                      } catch {
                        setShowTooltip(false);
                      }
                    }
                  : undefined
              }
              onMouseLeave={
                isPixelColorPickerEnabled
                  ? () => {
                      setShowTooltip(false);
                      setColorInfo(null);
                    }
                  : undefined
              }
            >
              <Layer ref={layerRef}>
                {/* Test Circle for Color Picker */}

                {(loggingViewStacks ?? []).map((loggingView) => {
                  if (
                    [
                      EnumLoggingViewStack.Image,
                      EnumLoggingViewStack.Below,
                      EnumLoggingViewStack.Overlay,
                    ].includes(loggingView.type)
                  ) {
                    return (
                      <ImageRowLogging
                        key={loggingView?.id}
                        addOCR={addOCR}
                        data={loggingView.data}
                        uiProps={loggingView.uiProps}
                        startY={loggingView.startY}
                        type={loggingView.type}
                        directOCRdata={directOCRdata}
                        onChangeText={onChangeText}
                        onDblClickText={onDblClickText}
                        onChange={onChange}
                        isShowOCR={isShowOCR && loggingView?.data?.isShowOCR}
                        image={image}
                        imageIndex={loggingView.index}
                        clickedDepthData={clickedDepthData}
                        setModalState={setModalState}
                        modalState={modalState}
                        setStateAddOcr={setStateAddOcr}
                        stateAddOcr={stateAddOcr}
                        isMeasuring={isMeasuring}
                        onMeasurePoint={handleMeasurePoint}
                        startPointLine={startPointLine}
                        recoveries={rockLineCalculationResultWithRowDisplays.filter(
                          (rockLineCalculationResult) =>
                            rockLineCalculationResult.rowDisplays.includes(
                              loggingView.index,
                            ),
                        )}
                        refreshImageData={refreshImageData}
                        modalStateEntryData={modalStateEntryData}
                        setModalStateEntryData={setModalStateEntryData}
                      />
                    );
                  }

                  if (loggingView.type === EnumLoggingViewStack.Point) {
                    return (
                      <Line
                        key={loggingView?.id}
                        y={loggingView.startY}
                        points={loggingView.data?.points?.flatMap((point) => [
                          point.x,
                          point.y,
                        ])}
                        stroke="black"
                        strokeWidth={6}
                        lineJoin="round"
                        lineCap="round"
                        tension={0.1} // smooth the line
                      />
                    );
                  }

                  if (loggingView.type === EnumLoggingViewStack.LoggingBar) {
                    const width = loggingView.data.width || 0;
                    const x = loggingView.data.x || 0;
                    return (
                      <Group key={loggingView?.id}>
                        <Rect
                          x={x}
                          y={loggingView.startY}
                          width={width}
                          height={loggingView.data.height || 20}
                          fill={loggingView.data.color || "#CCCCCC"}
                          stroke="#000"
                          strokeWidth={2}
                          opacity={0.9}
                          onClick={async () => {
                            const dataEntryId = loggingView.data.dataEntryId;
                            if (dataEntryId) {
                              const res =
                                await dataEntryRequest.getDataEntryById({
                                  dataEntryId,
                                });
                              if (res.state === RequestState.success) {
                                setEntryDataModal({
                                  isOpen: true,
                                  type: "update-bar",
                                  detail: res.data,
                                });
                              }
                            }

                            // Open modal to edit geology bar interval
                          }}
                        />
                      </Group>
                    );
                  }
                  return null;
                })}
              </Layer>
            </Stage>
          );
        }}
      </AutoSizerTmp>
      {legend && (
        <div
          className="absolute z-10 top-0 right-0 flex flex-col gap-3 border-l-2 p-2 w-[180px]"
          style={{ height: stageHeight, backgroundColor: "#f5f5f5" }}
        >
          <div className="flex gap-2 flex-col">
            <p>Rock Type Colour Query</p>
            <div className="flex items-center gap-2">
              <Switch
                checked={isPixelColorPickerEnabled}
                onChange={() => {
                  dispatch(togglePixelColorPicker());
                }}
              />
            </div>
          </div>

          <p className="font-bold text-sm">{rockGroupDetail?.name}</p>
          {rockGroupDetail?.rockTypes?.map((rockType) => (
            <div key={rockType.id} className="grid grid-cols-3 gap-2">
              <div
                className="w-full h-full rounded-full p-2"
                style={{ backgroundColor: rockType.rockStyle?.fillColor }}
              ></div>
              <p className="font-bold text-sm col-span-2">{rockType.code}</p>
            </div>
          ))}
        </div>
      )}

      {/* Pixel Color Picker Tooltip */}
      {showTooltip && colorInfo && (
        <PixelColorPicker
          tooltipPosition={tooltipPosition}
          colorInfo={colorInfo}
        />
      )}
    </div>
  );
}

export default memo(LoggingStage);
