import { useCallback, useEffect, useRef, useState } from "react";

import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";

import { getLoggingInfoInfinite } from "../redux/loggingSlice/thunks";

interface UseInfiniteScrollEntriesProps {
  geologySuiteId?: number;
  drillholeId?: number;
  initialPageSize?: number;
  pageSize?: number;
}

export const useInfiniteScrollEntries = ({
  geologySuiteId,
  drillholeId,
  initialPageSize = 20, // Load 20 items ban đầu
  pageSize = 20, // Load 20 items mỗi lần
}: UseInfiniteScrollEntriesProps) => {
  const dispatch = useAppDispatch();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Redux state with fallback defaults
  const { infiniteScrollLoggings, infiniteScrollState, getLoggingInfoStatus } =
    useAppSelector((state) => state.logging);

  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize infinite scroll data
  const initializeInfiniteScroll = useCallback(async () => {
    if (!geologySuiteId || !drillholeId || isInitialized) {
      return;
    }

    setIsInitialized(true);
    await dispatch(
      getLoggingInfoInfinite({
        GeologySuiteId: geologySuiteId,
        DrillholeId: drillholeId,
        skipCount: 0,
        maxResultCount: initialPageSize,
        reset: true,
      }),
    );
  }, [dispatch, geologySuiteId, drillholeId, initialPageSize, isInitialized]);

  // Load more data
  const loadMoreData = useCallback(async () => {
    if (!geologySuiteId || !drillholeId) {
      return;
    }
    if (infiniteScrollState?.loading || !infiniteScrollState?.hasMore) {
      return;
    }

    const nextSkipCount = (infiniteScrollState?.currentPage + 1) * pageSize;

    await dispatch(
      getLoggingInfoInfinite({
        GeologySuiteId: geologySuiteId,
        DrillholeId: drillholeId,
        skipCount: nextSkipCount,
        maxResultCount: pageSize,
        reset: false,
      }),
    );
  }, [
    dispatch,
    geologySuiteId,
    drillholeId,
    infiniteScrollState?.loading,
    infiniteScrollState?.hasMore,
    infiniteScrollState?.currentPage,
    pageSize,
  ]);

  // Scroll event handler
  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const target = event.currentTarget;
      const { scrollTop, scrollHeight, clientHeight } = target;

      // Load more when scrolled to within 200px of bottom (tăng threshold để load sớm hơn)
      const threshold = 200;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      if (
        distanceFromBottom < threshold &&
        infiniteScrollState?.hasMore &&
        !infiniteScrollState?.loading
      ) {
        loadMoreData();
      }
    },
    [loadMoreData, infiniteScrollState?.hasMore, infiniteScrollState?.loading],
  );

  // Reset scroll state when parameters change
  useEffect(() => {
    setIsInitialized(false);
  }, [geologySuiteId, drillholeId]);

  // Initialize on mount or parameter change
  useEffect(() => {
    initializeInfiniteScroll();
  }, [initializeInfiniteScroll]);

  return {
    // Data
    data: infiniteScrollLoggings || [],

    // Loading states
    isLoading: infiniteScrollState?.loading,
    isInitialLoading:
      getLoggingInfoStatus === "pending" &&
      (infiniteScrollLoggings || []).length === 0,
    hasMore: infiniteScrollState?.hasMore,

    // Scroll handling
    scrollContainerRef,
    handleScroll,

    // Manual controls
    loadMoreData,
    initializeInfiniteScroll,

    // Pagination info
    currentPage: infiniteScrollState?.currentPage,
    totalItems: infiniteScrollState?.totalItems,
    loadedItems: (infiniteScrollLoggings || []).length,
  };
};
