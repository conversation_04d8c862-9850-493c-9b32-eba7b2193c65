/* eslint-disable complexity */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
import { FolderOutlined } from "@ant-design/icons"; // Added
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, TreeSelect } from "antd";
import { useCallback, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { GiStonePile } from "react-icons/gi"; // Added
import { toast } from "react-toastify";

import { RequestState } from "@/common/configs/app.contants";
import { ModalType } from "@/common/configs/app.enum";
import { ButtonCommon } from "@/components/common/button-common";
import ColorPickerCommon from "@/components/common/color-picker";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListGeologyDate } from "@/modules/geology-date/hooks/useGetListGeologyDate";
import { useGetListGeologyDescription } from "@/modules/geology-description/hooks/useGetListGeologyDescription";
import {
  FieldType,
  FieldTypeOptions,
} from "@/modules/geology-suite-field/const/enum";
import { useGetListColour } from "@/modules/list/hooks/colour/useGetListColour";
import { useGetListNumberFields } from "@/modules/list/hooks/number-fields/useGetListNumberFields";
import { useGetListPickList } from "@/modules/list/hooks/pick-lists/useGetListPickList";
import { useGetListRockGroup } from "@/modules/rock-groups/hooks/useGetListRockGroups";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import rockTreeRequest, {
  TreeNode as ApiTreeNode,
} from "@/modules/rock-types-tree/api/rock-tree.api";

import { useGetListGeologyCheckbox } from "@/modules/geology-checkbox/hooks/useGetListGeologyCheckbox";
import { useGetListGeologyLatitude } from "@/modules/geology-latitude/hooks/useGetListGeologyLatitude";
import { useGetListGeologyLongitude } from "@/modules/geology-longitude/hooks/useGetListGeologyLongitude";
import { useGetListGeologyUrl } from "@/modules/geology-url/hooks/useGetListGeologyUrl";
import { useCreateGeologyField } from "../hooks/useCreateGeologyField";
import { useDeleteGeologyField } from "../hooks/useDeleteGeologyField";
import { useUpdateGeologyField } from "../hooks/useUpdateGeologyField";
import {
  GeologyFieldBody,
  GeologyFieldBodyType,
} from "../model/schema/geology-field.schema";

export interface IModalGeologyFieldProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export interface TransformedTreeNode {
  title: React.ReactNode; // Changed from string
  nameForSearch: string; // Added for filtering
  value: string | number;
  key: string | number;
  children?: TransformedTreeNode[];
  isLeaf?: boolean;
  // Store original API node data if needed for more complex styling decisions
  originalData: ApiTreeNode;
}

export function ModalGeologyField(props: IModalGeologyFieldProps) {
  const { modalState, setModalState, refresh } = props;
  const [searchValue, setSearchValue] = useState(""); // For other select components
  const [rockTreeData, setRockTreeData] = useState<TransformedTreeNode[]>([]); // Master data for RockTree
  const [rockTreeLoading, setRockTreeLoading] = useState(false);

  // States for TreeSelect filtering and expansion
  const [treeSearchValue, setTreeSearchValue] = useState("");
  const [currentRockTreeData, setCurrentRockTreeData] = useState<
    TransformedTreeNode[]
  >([]);
  const [treeExpandedKeys, setTreeExpandedKeys] = useState<(string | number)[]>(
    [],
  );

  const handleSearch = (value: string) => {
    setSearchValue(value); // For other select components
  };

  const {
    request: requestCreateGeologyField,
    loading: loadingCreateGeologyField,
  } = useCreateGeologyField();
  const {
    request: requestUpdateGeologyField,
    loading: loadingUpdateGeologyField,
  } = useUpdateGeologyField();
  const {
    request: requestDeleteGeologyField,
    loading: loadingDeleteGeologyField,
  } = useDeleteGeologyField();
  const { control, handleSubmit, setValue, getValues, watch, reset } =
    useForm<GeologyFieldBodyType>({
      resolver: zodResolver(GeologyFieldBody),
      defaultValues: {
        isActive: true,
        backgroundColour: "#000000",
        textColour: "#ffffff",
      },
    });

  // Default form values
  const DEFAULT_FORM_VALUES = {
    name: "",
    code: "",
    isActive: true,
    backgroundColour: "#000000",
    textColour: "#ffffff",
    type: undefined,
    rockNodeId: undefined,
    referenceId: undefined,
    rockTypeId: undefined,
    rockGroupId: undefined,
    numberId: undefined,
  };

  // Helper function to prepare form values for reset
  const prepareFormValues = () => {
    if (!modalState.detailInfo) {
      return DEFAULT_FORM_VALUES;
    }

    return {
      ...DEFAULT_FORM_VALUES,
      ...modalState.detailInfo,
      isActive: modalState.detailInfo.isActive ?? DEFAULT_FORM_VALUES.isActive,
      backgroundColour:
        modalState.detailInfo.backgroundColour ||
        DEFAULT_FORM_VALUES.backgroundColour,
      textColour:
        modalState.detailInfo.textColour || DEFAULT_FORM_VALUES.textColour,
    };
  };

  useEffect(() => {
    if (modalState.isOpen) {
      const valuesToReset = prepareFormValues();
      reset(valuesToReset);
    }
  }, [modalState.isOpen, JSON.stringify(modalState.detailInfo), reset]);

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;

  // Helper function to clean up form values based on field type
  const cleanFormValues = (
    values: GeologyFieldBodyType,
  ): Partial<GeologyFieldBodyType> => {
    const cleanedValues: Partial<GeologyFieldBodyType> = { ...values };

    // Clear irrelevant fields based on type
    if (values.type !== FieldType.RockTree) {
      delete cleanedValues.rockNodeId;
    }
    if (
      values.type !== FieldType.RockGroup &&
      values.type !== FieldType.PickList &&
      values.type !== FieldType.NumberField &&
      values.type !== FieldType.Description &&
      values.type !== FieldType.DateField &&
      values.type !== FieldType.Colour &&
      values.type !== FieldType.Checkbox &&
      values.type !== FieldType.Latitude &&
      values.type !== FieldType.Longitude &&
      values.type !== FieldType.Url
    ) {
      delete cleanedValues.referenceId;
    }
    if (values.type !== FieldType.RockType) {
      delete cleanedValues.rockTypeId;
    }
    if (values.type !== FieldType.RockSelect) {
      delete cleanedValues.rockGroupId;
    }
    if (
      values.type !== FieldType.RockType &&
      values.type !== FieldType.RockSelect
    ) {
      delete cleanedValues.numberId;
    }

    return cleanedValues;
  };

  // Helper function to handle form submission
  const handleFormSubmit = (cleanedValues: Partial<GeologyFieldBodyType>) => {
    const successCallback = () => {
      setModalState({ ...modalState, isOpen: false });
      toast.success(
        modalState.type === ModalType.CREATE
          ? "Create geology field successfully"
          : "Update geology field successfully",
      );
      refresh();
    };

    if (modalState.type === ModalType.CREATE) {
      requestCreateGeologyField(
        cleanedValues as GeologyFieldBodyType,
        successCallback,
      );
    } else if (modalState.type === ModalType.UPDATE) {
      requestUpdateGeologyField(
        {
          ...cleanedValues,
          id: modalState.detailInfo.id,
        } as GeologyFieldBodyType,
        successCallback,
      );
    }
  };

  const onSubmit = (values: GeologyFieldBodyType) => {
    const cleanedValues = cleanFormValues(values);
    handleFormSubmit(cleanedValues);
  };

  const handleDelete = () => {
    requestDeleteGeologyField(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        toast.error(err?.message);
      },
    );
  };
  const { request: requestGetListColour } = useGetListColour();
  const { data: listRockType, request: requestGetListRockType } =
    useGetListRockType();
  const { request: requestGetListNumberFields, data: listNumberField } =
    useGetListNumberFields();
  const { request: requestGetListRockGroup, data: listRockGroup } =
    useGetListRockGroup();
  const { request: requestGetListPickList } = useGetListPickList();
  const { request: requestGetListGeologyDate } = useGetListGeologyDate();
  const { request: requestGetListGeologyDescription } =
    useGetListGeologyDescription();
  const { request: requestGetListGeologyCheckbox } =
    useGetListGeologyCheckbox();
  const { request: requestGetListGeologyLatitude } =
    useGetListGeologyLatitude();
  const { request: requestGetListGeologyLongitude } =
    useGetListGeologyLongitude();
  const { request: requestGetListGeologyUrl } = useGetListGeologyUrl();
  const type = watch("type");
  const [listReference, setListReference] = useState<any[]>([]);

  // Helper function to create options from API response
  const createOptionsFromResponse = (data: any) => {
    return (
      data?.items?.map((item: any) => ({
        label: item.name,
        value: item.id,
      })) || []
    );
  };

  // Common API parameters
  const COMMON_API_PARAMS = {
    maxResultCount: 1000,
    skipCount: 0,
    keyword: searchValue,
  };

  // Helper function to handle API requests with common parameters
  const handleApiRequest = (
    requestFunction: (params: any, onSuccess?: Function) => void,
    onSuccess?: (data: any) => void,
  ) => {
    requestFunction(COMMON_API_PARAMS, onSuccess);
  };

  const transformApiNodeToTreeNode = (
    apiNode: ApiTreeNode,
  ): TransformedTreeNode => {
    const isRockNode = apiNode.nodeType === 1 || apiNode.nodeType === 2;
    const icon = isRockNode ? (
      <GiStonePile className="mr-2 text-lg" />
    ) : (
      <FolderOutlined className="mr-2 text-yellow-500 text-lg" />
    );

    return {
      title: (
        <span className="flex items-center">
          {icon}
          <span style={{ color: apiNode.displayColor || "inherit" }}>
            {apiNode.name}
          </span>
        </span>
      ),
      nameForSearch: apiNode.name, // For filtering
      value: apiNode.id,
      key: apiNode.id,
      originalData: apiNode, // Store original data
      isLeaf: apiNode.nodeType === 1 || apiNode.nodeType === 2, // Rocks are leaves, folders might have children
      children:
        apiNode.children && apiNode.children.length > 0
          ? apiNode.children.map(transformApiNodeToTreeNode)
          : undefined,
    };
  };

  const loadInitialRockTreeData = useCallback(async () => {
    setRockTreeLoading(true);
    try {
      const result = await rockTreeRequest.getTenantTreeStructure();
      if (result.state === RequestState.success && result.data) {
        const transformed = result.data.rootNodes.map(
          transformApiNodeToTreeNode,
        );
        setRockTreeData(transformed);
      } else {
        toast.error(result.message || "Failed to load rock tree data.");
        setRockTreeData([]);
      }
    } catch {
      toast.error("An error occurred while fetching rock tree data.");
      setRockTreeData([]);
    } finally {
      setRockTreeLoading(false);
    }
  }, []);

  useEffect(() => {
    if (type === FieldType.RockTree && modalState.isOpen) {
      loadInitialRockTreeData(); // This sets rockTreeData (master data)
    } else {
      setRockTreeData([]); // Clear master data
      // Also clear derived TreeSelect states if modal closes or type changes
      setCurrentRockTreeData([]);
      setTreeExpandedKeys([]);
      setTreeSearchValue("");
    }
  }, [type, modalState.isOpen]);

  // Helper function to generate filtered tree data and expanded keys
  const generateFilteredDataAndExpandedKeys = useCallback(
    (
      nodes: TransformedTreeNode[],
      currentSearchValue: string,
    ): {
      filteredData: TransformedTreeNode[];
      newExpandedKeys: (string | number)[];
    } => {
      const collectedKeys = new Set<string | number>();

      if (!currentSearchValue) {
        // If no search, return all nodes and collect all keys for expansion
        const collectAllKeysRecursive = (
          nodeList: TransformedTreeNode[],
          keysSet: Set<React.Key>,
        ) => {
          nodeList.forEach((node) => {
            keysSet.add(node.key);
            if (node.children) {
              collectAllKeysRecursive(node.children, keysSet);
            }
          });
        };
        collectAllKeysRecursive(nodes, collectedKeys);
        return {
          filteredData: nodes,
          newExpandedKeys: Array.from(collectedKeys),
        };
      }

      const filter = (
        nodeList: TransformedTreeNode[],
      ): TransformedTreeNode[] => {
        return nodeList.reduce((acc, node) => {
          const nodeMatches = node.nameForSearch
            .toLowerCase()
            .includes(currentSearchValue.toLowerCase());
          let filteredChildren: TransformedTreeNode[] | undefined;
          let hasVisibleChild = false;

          if (node.children && node.children.length > 0) {
            filteredChildren = filter(node.children); // Recursive call
            if (filteredChildren.length > 0) {
              hasVisibleChild = true;
            }
          }

          if (nodeMatches || hasVisibleChild) {
            collectedKeys.add(node.key); // Expand this node
            acc.push({
              ...node,
              children: filteredChildren, // Use the filtered children
            });
          }
          return acc;
        }, [] as TransformedTreeNode[]);
      };

      const filteredData = filter(nodes);
      return { filteredData, newExpandedKeys: Array.from(collectedKeys) };
    },
    [],
  );

  useEffect(() => {
    // This effect derives currentRockTreeData and treeExpandedKeys from rockTreeData and treeSearchValue
    if (type === FieldType.RockTree && modalState.isOpen) {
      const { filteredData, newExpandedKeys } =
        generateFilteredDataAndExpandedKeys(rockTreeData, treeSearchValue);
      setCurrentRockTreeData(filteredData);
      setTreeExpandedKeys(newExpandedKeys);
    }
    // No 'else' here to clear setCurrentRockTreeData/setTreeExpandedKeys,
    // as the useEffect for loadInitialRockTreeData handles clearing when modal closes or type changes.
  }, [rockTreeData, treeSearchValue, type, modalState.isOpen]);

  useEffect(() => {
    const handleReferenceData = (data: any) => {
      setListReference(createOptionsFromResponse(data));
    };

    if (type === FieldType.Colour) {
      handleApiRequest(requestGetListColour, handleReferenceData);
    } else if (type === FieldType.NumberField) {
      handleApiRequest(requestGetListNumberFields, handleReferenceData);
    } else if (type === FieldType.RockGroup) {
      handleApiRequest(requestGetListRockGroup, handleReferenceData);
    } else if (type === FieldType.PickList) {
      handleApiRequest(requestGetListPickList, handleReferenceData);
    } else if (type === FieldType.RockSelect) {
      requestGetListRockGroup(COMMON_API_PARAMS);
      handleApiRequest(requestGetListNumberFields, handleReferenceData);
    } else if (type === FieldType.DateField) {
      handleApiRequest(requestGetListGeologyDate, handleReferenceData);
    } else if (type === FieldType.RockType) {
      requestGetListRockType(COMMON_API_PARAMS);
      requestGetListNumberFields(COMMON_API_PARAMS);
    } else if (type === FieldType.Description) {
      handleApiRequest(requestGetListGeologyDescription, handleReferenceData);
    } else if (type === FieldType.Checkbox) {
      handleApiRequest(requestGetListGeologyCheckbox, handleReferenceData);
    } else if (type === FieldType.Latitude) {
      handleApiRequest(requestGetListGeologyLatitude, handleReferenceData);
    } else if (type === FieldType.Longitude) {
      handleApiRequest(requestGetListGeologyLongitude, handleReferenceData);
    } else if (type === FieldType.Url) {
      handleApiRequest(requestGetListGeologyUrl, handleReferenceData);
    } else {
      setListReference([]);
    }
  }, [type, searchValue]);

  useEffect(() => {
    if (modalState.detailInfo?.type === FieldType.RockType) {
      setValue(
        "rockTypeId",
        modalState.detailInfo?.rockTypeNumber?.rockType?.id,
      );
      setValue("numberId", modalState.detailInfo?.rockTypeNumber?.number?.id);
    }
    if (modalState.detailInfo?.type === FieldType.RockSelect) {
      setValue(
        "rockGroupId",
        modalState.detailInfo?.rockSelectNumber?.rockGroup?.id,
      );
      setValue("numberId", modalState.detailInfo?.rockSelectNumber?.number?.id);
    }
    if (modalState.detailInfo?.type === FieldType.RockTree) {
      setValue("rockNodeId", modalState.detailInfo?.rockNode?.id);
    }
  }, [modalState.detailInfo]);

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this geology field?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            geology field.
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteGeologyField}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4 py-6">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update Geology Field"
              : "Add Geology Field"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type geology field name here"
              control={control}
              isRequired={true}
            />
            <InputTextCommon
              label="Code "
              name="code"
              placeholder="Type geology field code here"
              control={control}
              isRequired={true}
            />

            <ColorPickerCommon
              getValues={getValues}
              setValue={setValue}
              control={control}
              name="backgroundColour"
              label="Background Colour"
              placeholder="Choose background colour here"
              isRequired={true}
            />
            <ColorPickerCommon
              getValues={getValues}
              setValue={setValue}
              control={control}
              name="textColour"
              label="Text Colour"
              placeholder="Choose text colour here"
              isRequired={true}
            />

            <SelectCommon
              label="Field Type"
              name="type"
              placeholder="Select field type"
              control={control}
              options={FieldTypeOptions.sort((a, b) =>
                a.label.localeCompare(b.label),
              )}
              onChange={() => {
                setValue("referenceId", undefined);
                setValue("numberId", undefined);
                setValue("rockGroupId", undefined);
                setValue("rockTypeId", undefined);
                setValue("rockNodeId", undefined);
              }}
              isRequired={true}
            />

            {type === FieldType.RockTree && (
              <Controller
                name="rockNodeId"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Form.Item
                    label="Rock Tree"
                    validateStatus={error ? "error" : ""}
                    help={error?.message}
                    required={false} // Set based on your schema if it's required
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                  >
                    <TreeSelect
                      {...field}
                      showSearch
                      style={{ width: "100%" }}
                      dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                      placeholder="Please select from rock tree"
                      allowClear
                      treeData={currentRockTreeData} // Use filtered/derived data
                      loading={rockTreeLoading}
                      // loadData={onLoadRockTreeData} // Keep original commented status
                      onSearch={setTreeSearchValue} // Set TreeSelect's own search value
                      treeExpandedKeys={treeExpandedKeys}
                      onTreeExpand={(keys: React.Key[]) => {
                        const safeKeys = keys.filter(
                          (k): k is string | number =>
                            typeof k === "string" || typeof k === "number",
                        );
                        setTreeExpandedKeys(safeKeys);
                      }}
                      filterTreeNode={false} // Signal that data is pre-filtered
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </Form.Item>
                )}
              />
            )}

            {(type === FieldType.RockGroup ||
              type === FieldType.PickList ||
              type === FieldType.NumberField ||
              type === FieldType.Description ||
              type === FieldType.Checkbox ||
              type === FieldType.Latitude ||
              type === FieldType.Longitude ||
              type === FieldType.Url ||
              type === FieldType.DateField) && (
              <SelectCommon
                label="Reference"
                name="referenceId"
                placeholder="Select reference"
                control={control}
                options={listReference}
                allowClear
                showSearch
                searchValue={searchValue}
                onSearch={handleSearch}
                filterOption={false}
                // isRequired={type === FieldType.Colour} // Example: Colour might require a reference
              />
            )}
            {type === FieldType.RockType && (
              <>
                <SelectCommon
                  label="Rock Type"
                  name="rockTypeId"
                  placeholder="Select rock type"
                  control={control}
                  options={listRockType?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  allowClear
                  showSearch
                  searchValue={searchValue}
                  onSearch={handleSearch}
                  filterOption={false}
                />
                <SelectCommon
                  label="Number Field"
                  name="numberId"
                  placeholder="Select number field"
                  control={control}
                  options={listNumberField?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  allowClear
                  showSearch
                  searchValue={searchValue}
                  onSearch={handleSearch}
                  filterOption={false}
                />
              </>
            )}
            {type === FieldType.RockSelect && (
              <>
                <SelectCommon
                  label="Rock Group"
                  name="rockGroupId"
                  placeholder="Select rock group"
                  control={control}
                  options={listRockGroup?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  allowClear
                  showSearch
                  searchValue={searchValue}
                  onSearch={handleSearch}
                  filterOption={false}
                />
                <SelectCommon
                  label="Number Field"
                  name="numberId"
                  placeholder="Select number field"
                  control={control}
                  options={listNumberField?.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  allowClear
                  showSearch
                  searchValue={searchValue}
                  onSearch={handleSearch}
                  filterOption={false}
                />
              </>
            )}

            <ToogleCommon label="Is Active" name="isActive" control={control} />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingCreateGeologyField || loadingUpdateGeologyField}
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update Geology Field"
                  : "Create Geology Field"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
