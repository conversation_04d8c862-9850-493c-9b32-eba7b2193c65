import { SortOrder } from "@/common/interfaces/general/general.types";
import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import downholeDataRequest from "@/modules/downhole-point/api/down-hole-data.api";
import dataEntryRequest from "@/modules/logging/api/data-entry.api";

import imageRequest from "../../api/image.api";
import { DrillHoleAttributePoints } from "../../model/entities/drillhole.config";
import {
  updateAttributePoints,
  updateAttributePointsCache,
} from "../imageSlice";

export const getImages = createAppAsyncThunk(
  "images/list",
  async (query: any) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const keys = Object.keys(otherQueries);

    const listDrillhole: any[] = keys
      .filter((key) => key.startsWith("DrillholeIds"))
      .map((key) => {
        const match = key.match(/\d+$/);
        return match ? Number(match[0]) : null;
      });

    const response = await imageRequest.getImages({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      sortField: otherQueries.sortField || "depthFrom",
      sortOrder: otherQueries.sortOrder || SortOrder.ASC,
      projectIds: otherQueries?.projectId && [Number(otherQueries.projectId)],
      prospectIds: otherQueries?.prospectId && [
        Number(otherQueries.prospectId),
      ],
      holeIds:
        listDrillhole.length && !listDrillhole.includes(0)
          ? listDrillhole
          : undefined,
      depthFrom: otherQueries.DepthFrom,
      depthTo: otherQueries.DepthTo,
      imageStatus: otherQueries.ImageStatus,
    });
    return response;
  },
);

export const getGeophysicsData = createAppAsyncThunk(
  "images/getGeophysicsData",
  async (
    params: {
      projectId: string;
      selectedAttributes: string[];
      selectedHoleNames: string[];
    },
    { getState, dispatch },
  ) => {
    const { images } = getState();
    const { selectedAttributes, selectedHoleNames } = params;
    const allPointsDatas = [...images.attributePointsCache];

    for (let i = 0; i < selectedAttributes.length; i++) {
      for (let j = 0; j < selectedHoleNames.length; j++) {
        let pointsData = {} as DrillHoleAttributePoints;

        // Check if the attribute points are already in cache
        const isInCache = images.attributePointsCache.find(
          (point) =>
            point.type === selectedAttributes[i] &&
            point.drillHoleName === selectedHoleNames[j],
        );

        if (isInCache) {
          // If the attribute points are in cache, get them based on selected attributes
          pointsData = isInCache;
        } else {
          // Not in cache, fetch from API
          // Ensure DrillHoleName is always passed as an array of strings
          const response = await downholeDataRequest.getData({
            projectId: params.projectId,
            AttributeName: selectedAttributes[i],
            DrillHoleName: selectedHoleNames[j],
          });
          const coor: DrillHoleAttributePoints[] = (response?.data?.items ?? [])
            .filter(
              (attribute) =>
                !isNaN(Number(attribute?.["Depth (m)"])) &&
                !isNaN(Number(attribute?.[selectedAttributes[i]])),
            )

            .map((attribute) => {
              return {
                x: Number(attribute?.["Depth (m)"]),
                y: Number(attribute?.[selectedAttributes[i]]),
              };
            });

          pointsData = {
            type: selectedAttributes[i],
            drillHoleName: selectedHoleNames[j],
            coor: coor,
          };

          if (
            !allPointsDatas.find(
              (point) =>
                point.type === selectedAttributes[i] &&
                point.drillHoleName === selectedHoleNames[j],
            )
          ) {
            allPointsDatas.push(pointsData);
          }
        }
      }
    }
    dispatch(updateAttributePointsCache(allPointsDatas));
    dispatch(
      updateAttributePoints(
        allPointsDatas.filter((point) =>
          params.selectedAttributes.includes(point.type),
        ),
      ),
    );
  },
);

export const getMultiGeologyData = createAppAsyncThunk(
  "images/getMultiGeologyData",
  async (query: {
    drillHoles: {
      DrillholeId?: any;
      DrillholeName?: any;
    }[];
    suites: {
      suiteId: number;
      suiteName: string;
    }[];
  }) => {
    const multiGeologyDatas: any = [];
    for (const suite of query.suites) {
      for (const drillHole of query.drillHoles) {
        const geologyDatas = await dataEntryRequest.getAllDataEntry({
          GeologySuiteId: suite.suiteId,
          DrillholeName: drillHole.DrillholeName,
          // DrillholeId: 350,
        });
        multiGeologyDatas.push({
          suiteId: suite.suiteId,
          suiteName: suite.suiteName,
          drillHoleId: drillHole.DrillholeName,
          entries: geologyDatas.data?.items,
        });
      }
    }
    return multiGeologyDatas;
  },
);
