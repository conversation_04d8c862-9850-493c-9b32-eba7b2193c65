/* eslint-disable max-params */
import { isNumber, max } from "lodash";

import { DrillHoleImages } from "../components/drillhole-view";
import type { SelectedNodeList } from "../interface/image.interface";
import { EnumDrillholeView } from "../model/enum/drillhole.enum";
import { ImageSizeEnum } from "../model/enum/images.enum";

export const getAvailableImage = (
  imageList: any[] = [],
  targetSize: ImageSizeEnum,
) => {
  const imageSizes = [
    ImageSizeEnum.SMALL,
    ImageSizeEnum.THUMBNAIL,
    ImageSizeEnum.MEDIUM,
    ImageSizeEnum.LARGE,
    ImageSizeEnum.FULL_SIZE,
  ];

  const targetSizeIndex = imageSizes.indexOf(targetSize);
  for (let i = targetSizeIndex; i < imageSizes.length; i++) {
    const targetImage = imageList.find((image) => image.size === imageSizes[i]);
    if (targetImage) {
      return targetImage;
    }
  }
};

/******** Calculate image data ********/
export const reCalculateImageSize = (
  dhImages: DrillHoleImages,
  viewMode: EnumDrillholeView,
  displayMode: ImageSizeEnum,
) => {
  switch (viewMode) {
    case EnumDrillholeView.Original: {
      let minHeight = Infinity;
      let maxHeight = -Infinity;
      let maxWidth = 0;

      const listImages = (dhImages?.list ?? []).map((image) => {
        const viewImage = getAvailableImage(image?.files ?? [], displayMode);
        const height = image.depthTo - image.depthFrom;
        let width = (viewImage?.width / viewImage?.height) * height;

        if (!isNumber(width) || isNaN(width) || width === 0) {
          width = 7;
        }
        maxWidth = Math.max(maxWidth, width);
        if (image.depthFrom < minHeight) {
          minHeight = image.depthFrom;
        }
        if (image.depthTo > maxHeight) {
          maxHeight = image.depthTo;
        }
        return {
          ...image,
          displayUrl: viewImage?.url,
          width: width,
          height: height,
        };
      });
      return {
        list: listImages,
        minHeight: minHeight,
        maxHeight: maxHeight,
        maxWidth: maxWidth,
        rotation: 0,
      };
    }
    case EnumDrillholeView.Box: {
      let minHeight = Infinity;
      let maxHeight = -Infinity;
      let maxWidth = 0;

      const croppedBoxImages: {
        urlCroppedImage: string;
        id: string;
        coordinate: string;
        depthFrom: number;
        depthTo: number;
        mediumSize: string;
      }[] = (dhImages?.list ?? [])
        .filter((image) => (image?.croppedImages ?? []).length > 0)
        .reduce((previous, currrent) => {
          const croppedImages = [...currrent?.croppedImages].filter((image) => {
            return image?.type?.toLowerCase() === "box";
          });
          if (!croppedImages || croppedImages?.length === 0) {
            return previous;
          }

          return [
            ...previous,
            ...croppedImages.map((image) => ({
              urlCroppedImage: image?.urlCroppedImage,
              mediumSize: image?.mediumSize,
              id: image.id,
              coordinate: image.coordinate ?? "{}",
              depthFrom: parseFloat(image.depthFrom.toFixed(2)),
              depthTo: parseFloat(image.depthTo.toFixed(2)),
            })),
          ];
        }, []);

      const listImages = croppedBoxImages.map((image) => {
        const viewImage = image?.mediumSize ?? image?.urlCroppedImage;
        const width = JSON.parse(image?.coordinate ?? "{}")?.Width ?? 0;
        const height = JSON.parse(image?.coordinate ?? "{}")?.Height ?? 0;
        const viewHeight = image.depthTo - image.depthFrom;
        const viewWidth = (width / height) * viewHeight;

        maxWidth = Math.max(maxWidth, viewWidth);
        if (image.depthFrom < minHeight) {
          minHeight = image.depthFrom;
        }
        if (image.depthTo > maxHeight) {
          maxHeight = image.depthTo;
        }
        return {
          ...image,
          displayUrl: viewImage,
          width: viewWidth,
          height: viewHeight,
        };
      });
      return {
        list: listImages,
        minHeight: minHeight,
        maxHeight: maxHeight,
        maxWidth: maxWidth,
        rotation: 0,
      };
    }
    case EnumDrillholeView.Row: {
      let minHeight = Infinity;
      let maxHeight = -Infinity;
      let maxWidth = 0;
      const croppedRowImages: {
        urlCroppedImage: string;
        id: string;
        coordinate: string;
        depthFrom: number;
        depthTo: number;
        mediumSize: string;
      }[] = (dhImages?.list ?? [])
        .filter((image) => (image?.croppedImages ?? []).length > 0)
        .reduce((previous, currrent) => {
          const croppedImages = [...currrent?.croppedImages].filter(
            (image) => image?.type?.toLowerCase() === "row",
          );
          if (!croppedImages || croppedImages?.length === 0) {
            return previous;
          }

          return [
            ...previous,
            ...croppedImages.map((image) => ({
              urlCroppedImage: image?.urlCroppedImage,
              mediumSize: image?.mediumSize,
              id: image.id,
              coordinate: image.coordinate ?? "{}",
              depthFrom: parseFloat(image.depthFrom.toFixed(2)),
              depthTo: parseFloat(image.depthTo.toFixed(2)),
            })),
          ];
        }, []);

      const listImages = croppedRowImages.map((image) => {
        const viewImage = image?.mediumSize ?? image?.urlCroppedImage;
        const width = JSON.parse(image?.coordinate ?? "{}")?.Width ?? 0;
        const height = JSON.parse(image?.coordinate ?? "{}")?.Height ?? 0;
        const viewHeight = image.depthTo - image.depthFrom;
        const viewWidth = (width / height) * viewHeight;

        maxWidth = Math.max(maxWidth, viewWidth);
        if (image.depthFrom < minHeight) {
          minHeight = image.depthFrom;
        }
        if (image.depthTo > maxHeight) {
          maxHeight = image.depthTo;
        }
        return {
          ...image,
          displayUrl: viewImage,
          width: viewWidth,
          height: viewHeight,
        };
      });
      return {
        list: listImages,
        minHeight: minHeight,
        maxHeight: maxHeight,
        maxWidth: maxWidth,
        rotation: 0,
      };
    }
    case EnumDrillholeView.DownHole: {
      let minHeight = Infinity;
      let maxHeight = -Infinity;
      let maxWidth = 0;

      const croppedRowImages: {
        urlCroppedImage: string;
        id: string;
        coordinate: string;
        depthFrom: number;
        depthTo: number;
        mediumSize: string;
      }[] = (dhImages?.list ?? [])
        .filter((image) => (image?.croppedImages ?? []).length > 0)
        .reduce((previous, currrent) => {
          const croppedImages = [...currrent?.croppedImages].filter(
            (image) => image?.type?.toLowerCase() === "row",
          );
          if (!croppedImages || croppedImages?.length === 0) {
            return previous;
          }

          return [
            ...previous,
            ...croppedImages.map((image) => ({
              urlCroppedImage: image?.urlCroppedImage,
              mediumSize: image?.mediumSize,
              id: image.id,
              coordinate: image.coordinate ?? "{}",
              depthFrom: parseFloat(image.depthFrom.toFixed(2)),
              depthTo: parseFloat(image.depthTo.toFixed(2)),
            })),
          ];
        }, []);

      const listImages = croppedRowImages.map((image) => {
        const viewImage = image?.mediumSize ?? image?.urlCroppedImage;
        const width = JSON.parse(image?.coordinate ?? "{}")?.Width ?? 0;
        const height = JSON.parse(image?.coordinate ?? "{}")?.Height ?? 0;
        const viewHeight = image.depthTo - image.depthFrom;
        const viewWidth = (height / width) * viewHeight;

        maxWidth = Math.max(maxWidth, viewWidth);
        if (image.depthFrom < minHeight) {
          minHeight = image.depthFrom;
        }
        if (image.depthTo > maxHeight) {
          maxHeight = image.depthTo;
        }
        return {
          ...image,
          displayUrl: viewImage,
          width: viewHeight,
          height: viewWidth,
        };
      });
      return {
        list: listImages,
        minHeight: minHeight,
        maxHeight: maxHeight,
        maxWidth: maxWidth,
        rotation: 90,
      };
    }
    default: {
      return {
        list: [],
        minHeight: 0,
        maxHeight: 0,
        maxWidth: 0,
        rotation: 0,
      };
    }
  }
};

/******** Calculate point data ********/
export const resizePointData = (pointDatas: any[], viewWidth = 5) => {
  const minHeight = Math.min(...pointDatas.map((point) => point.x));
  const maxHeight = Math.max(...pointDatas.map((point) => point.x));
  const maxWidth = Math.max(...pointDatas.map((point) => point.y));
  const sortPointDatas = [...pointDatas].sort((a, b) => a.x - b.x);

  const scaleValue = maxWidth / viewWidth;
  const data = sortPointDatas.map((point) => ({
    x: point.x,
    y: point.y / scaleValue,
  }));
  return {
    data,
    minHeight,
    maxHeight,
    maxWidth: maxWidth / scaleValue,
  };
};

/******** Calculate point data ********/
export const resizeLoggingPointData = (
  pointDatas: any[],
  depthFrom: number,
  depthTo: number,
  imageRowWidth: number,
  maxViewHeight: number,
) => {
  const maxHeight = max(pointDatas.map((point) => Number(point.y)));
  const scaleValue = (maxHeight ?? 0) / maxViewHeight;

  const data = pointDatas.map((point) => {
    const xPoint = Number(point.x);
    const yPoint = Number(point.y);
    return {
      x: ((xPoint - depthFrom) / (depthTo - depthFrom)) * imageRowWidth,
      y: yPoint / scaleValue,
    };
  });
  return {
    points: data,
  };
};

/******** Transform selected values into hierarchical structure ********/
// Utility function to transform selected values into hierarchical structure
export const transformToHierarchicalNodes = (
  selectedValues: string[],
  imageTypeTreeData: any[],
): SelectedNodeList => {
  const selectNodes: SelectedNodeList["selectNodes"] = [];
  selectedValues.forEach((value) => {
    if (value.startsWith("type-")) {
      const selectedTypes = imageTypeTreeData.find((treeData) => {
        return treeData.value === value;
      });

      if (selectedTypes && selectedTypes.children.length > 0) {
        selectedTypes.children.forEach((child: any) => {
          selectNodes.push({
            id: child.value,
            typeId: Number(child.value.split("-")[3]),
            subTypeId: Number(child.value.split("-")[1]),
          });
        });
      }

      if (selectedTypes?.children?.length === 0) {
        selectNodes.push({
          id: value,
          typeId: Number(value.split("-")[1]),
        });
      }
    } else if (value.startsWith("subtype-")) {
      const subtypeId = value.split("-")[1];
      const typeId = value.split("-")[3];
      selectNodes.push({
        id: typeId,
        typeId: Number(typeId),
        subTypeId: Number(subtypeId),
      });
    }
  });

  return { selectNodes };
};
