import { CommonQuery } from "@/common/interfaces/request/ICommonQuery";
export interface ImageQuery extends CommonQuery {
  keyword?: string;
  projectIds?: any[];
  prospectIds?: any[];
  holeIds?: any[];
  depthFrom?: number;
  depthTo?: number;
  imageCategory?: number;
  imageStatus?: any;
  imageTypeId?: number; // integer($int32)
  imageSubtypeId?: number; // integer($int32)
  drillHoleNames?: any[];
  depth?: number;
  ignoreFields?: string[];
  imageSubTypeId?: number;
}

export interface IImageSubtype {
  id: string;
  name: string;
  imageTypeId: string;
  isActive?: boolean;
  isWet?: boolean;
  isDry?: boolean;
  // ... other properties
}

export interface IImageType {
  id: number;
  name: string;
  imageSubtypes: IImageSubtype[];
  isStandard?: boolean;
  isActive?: boolean;
  isRigCorrected?: boolean;
  priority?: number | null;
  // ... other properties
}

export interface StructuredSelectionItem {
  imageType: IImageType;
  selectedSubtypes: IImageSubtype[];
}

export interface SelectedNode {
  id: string;
  typeId: number;
  subTypeId?: number;
}

export interface SelectedNodeList {
  selectNodes: SelectedNode[];
}

export interface UploadFiles {
  file: any;
  url: string;
  status?: "valid" | "done" | "error";
  description?: string;
  errorMessage?: string;
  errorType?: "errorName";
  reLoading?: boolean;
  depthFrom?: number;
}

export interface IImageFilter {
  imageTypeId: number;
  imageSubtypeId?: number;
}

export interface ImageViewQuery extends CommonQuery {
  holeIds?: any[];
  drillHoleNames?: any[];
  imageCategory?: number;
  imageFilter?: IImageFilter[];
}
