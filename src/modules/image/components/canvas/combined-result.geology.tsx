import { isNumber } from "lodash";
import { memo, useMemo } from "react";
import { Group, Text } from "react-konva";

import { useAppSelector } from "@/common/vendors/redux/store/hook";

import { DrillHoleViewStack } from "../../model/entities/drillhole.config";
import { selectDHViewConfig, selectDHViewInfo } from "../../redux/imageSlice";
import TableKonva from "./table.konva";
interface Props {
  intervals: DrillHoleViewStack;
}

const CombinedResultColumn = ({ intervals }: Props) => {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const dHViewInfo = useAppSelector(selectDHViewInfo);
  const isTextWide = useAppSelector((state) => state.images.isTextWide);

  const tableData = [[intervals?.drillhole], [intervals?.extraInfo], ["Log"]];

  // Calculate text width and create a unique identifier for forcing re-renders
  const textWidth = useMemo(() => {
    return intervals.maxWidth;
  }, [intervals.maxWidth]);

  // Create a unique key that includes the text width state to force re-rendering
  const textWidthKey = isTextWide ? "wide" : "narrow";

  return (
    <Group>
      {!dHViewInfo?.isHideTableInfo && (
        <TableKonva
          startX={intervals.startX}
          startY={
            isNumber(intervals.minHeight)
              ? intervals.minHeight -
                (tableData.length + 1) * dHViewConfigs.cellHeight
              : 0
          }
          data={tableData}
          cellHeight={dHViewConfigs.cellHeight}
          cellWidth={dHViewConfigs.pointDataWidth / 2.3}
          fontSize={dHViewConfigs.fontSize}
          strokeWidth={dHViewConfigs.strokeWidth}
        />
      )}
      {(intervals.data ?? []).map((interval, index) => {
        if (!isNumber(interval?.depthTo) || !isNumber(interval?.depthFrom)) {
          return null;
        }

        return (
          <Group key={`${intervals?.id}-${index}-${textWidthKey}`}>
            {/* Depth From Text */}
            {/* {dHViewInfo?.isShowTextDepth && (
              <Text
                x={
                  intervals.startX -
                  (String(
                    isNumber(interval.depthFrom)
                      ? interval.depthFrom.toFixed(2)
                      : interval.depthFrom
                  ).length +
                    2) *
                    (dHViewConfigs.depthFromSize / 2) -
                  0.08
                }
                y={interval.depthFrom - dHViewConfigs.depthFromSize / 2}
                text={`${
                  isNumber(interval.depthFrom)
                    ? interval.depthFrom.toFixed(2)
                    : interval.depthFrom
                } -`}
                fontSize={dHViewConfigs.depthFromSize}
                fill="black"
              />
            )} */}

            {/* Combined Result Text */}
            <Text
              key={`text-${intervals?.id}-${index}-${textWidthKey}`}
              text={interval.combineResultText}
              fontSize={dHViewConfigs.fontSize}
              width={textWidth}
              x={intervals.startX}
              y={interval.depthFrom - dHViewConfigs.fontSize / 2.6}
              fill="#000000"
              wrap="word"
              ellipsis={!isTextWide}
              align="left"
              verticalAlign="middle"
            />

            {/* Depth To Text */}
            {/* {dHViewInfo?.isShowTextDepth && (
              <Text
                x={
                  intervals.startX -
                  (String(
                    isNumber(interval.depthTo)
                      ? interval.depthTo.toFixed(2)
                      : interval.depthTo
                  ).length +
                    2) *
                    (dHViewConfigs.depthToSize / 2) -
                  0.08
                }
                y={interval.depthTo - dHViewConfigs.depthToSize / 2}
                text={`${
                  isNumber(interval.depthTo)
                    ? interval.depthTo.toFixed(2)
                    : interval.depthTo
                } -`}
                fontSize={dHViewConfigs.depthToSize}
                fill="black"
              />
            )} */}
          </Group>
        );
      })}
    </Group>
  );
};

export default memo(CombinedResultColumn, (prevProps, nextProps) => {
  // Custom comparison function to ensure re-render when intervals change
  // We don't include isTextWide in the comparison because it's handled by Redux
  return (
    prevProps.intervals === nextProps.intervals &&
    prevProps.intervals?.id === nextProps.intervals?.id &&
    prevProps.intervals?.maxWidth === nextProps.intervals?.maxWidth
  );
});
