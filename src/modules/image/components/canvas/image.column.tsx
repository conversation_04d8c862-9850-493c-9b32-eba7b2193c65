import { isNumber } from "lodash";
import { memo, useCallback, useState } from "react";
import { createPortal } from "react-dom";
import { Group, Text } from "react-konva";

import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";

import { getAvailableImage } from "../../helpers/image.helpers";
import { DrillHoleViewStack } from "../../model/entities/drillhole.config";
import { EnumDrillholeView } from "../../model/enum/drillhole.enum";
import { ImageSizeEnum } from "../../model/enum/images.enum";
import {
  selectDHViewConfig,
  selectDHViewInfo,
  selectImages,
  updateDHViewInfo,
} from "../../redux/imageSlice";
import TableKonva from "./table.konva";
import ZoomableImage from "./zoomable-image";

// Tooltip component for displaying image information
interface TooltipProps {
  visible: boolean;
  position: { x: number; y: number };
  drillhole: string;
  typeName?: string;
  subTypeName?: string;
}

const ImageTooltip = ({
  visible,
  position,
  drillhole,
  typeName,
  subTypeName,
}: TooltipProps) => {
  if (!visible) {
    return null;
  }

  return createPortal(
    <div
      style={{
        position: "fixed",
        left: position.x + 10,
        top: position.y - 10,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        color: "white",
        padding: "8px 12px",
        borderRadius: "4px",
        fontSize: "12px",
        zIndex: 9999,
        pointerEvents: "none",
        maxWidth: "200px",
        wordWrap: "break-word",
      }}
    >
      <div>
        <strong>Drillhole:</strong> {drillhole}
      </div>
      {typeName && (
        <div>
          <strong>Type:</strong> {typeName}
        </div>
      )}
      {subTypeName && (
        <div>
          <strong>Subtype:</strong> {subTypeName}
        </div>
      )}
    </div>,
    document.body,
  );
};

const ImageColumn = ({ images }: { images: DrillHoleViewStack }) => {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const dHViewInfo = useAppSelector(selectDHViewInfo);
  const previewImageSize = useAppSelector(selectImages)?.previewSize;
  const drillHoleViewMode = useAppSelector(selectImages).drillholeViewMode;

  const dispatch = useAppDispatch();

  // Tooltip state management
  const [tooltip, setTooltip] = useState({
    visible: false,
    position: { x: 0, y: 0 },
  });

  // Handle mouse enter on image - show tooltip immediately
  const handleImageMouseEnter = useCallback((e: any) => {
    const stage = e.target.getStage();
    const pointerPosition = stage.getPointerPosition();

    if (pointerPosition) {
      // Convert stage coordinates to screen coordinates
      const stageContainer = stage.container();
      const rect = stageContainer.getBoundingClientRect();

      setTooltip({
        visible: true,
        position: {
          x: rect.left + pointerPosition.x,
          y: rect.top + pointerPosition.y,
        },
      });
    }
  }, []);

  // Handle mouse leave on image - hide tooltip immediately
  const handleImageMouseLeave = useCallback(() => {
    setTooltip((prev) => ({ ...prev, visible: false }));
  }, []);

  // Handle mouse move on image - update tooltip position
  const handleImageMouseMove = useCallback(
    (e: any) => {
      const stage = e.target.getStage();
      const pointerPosition = stage.getPointerPosition();

      if (pointerPosition && tooltip.visible) {
        // Convert stage coordinates to screen coordinates
        const stageContainer = stage.container();
        const rect = stageContainer.getBoundingClientRect();

        setTooltip((prev) => ({
          ...prev,
          position: {
            x: rect.left + pointerPosition.x,
            y: rect.top + pointerPosition.y,
          },
        }));
      }
    },
    [tooltip.visible],
  );

  const handlePreview = (image) => {
    if (drillHoleViewMode === EnumDrillholeView.Original) {
      const url = getAvailableImage(image.files, previewImageSize)?.url;
      dispatch(
        updateDHViewInfo({
          urlPreview: url,
        }),
      );
      return;
    }
    if (previewImageSize === ImageSizeEnum.FULL_SIZE) {
      dispatch(
        updateDHViewInfo({
          urlPreview: image.urlCroppedImage,
        }),
      );
      return;
    }
    const availableMediumSize = image?.mediumSize ?? image.urlCroppedImage;
    dispatch(
      updateDHViewInfo({
        urlPreview: availableMediumSize,
      }),
    );
  };

  const tableData = [
    [images?.drillhole],
    images?.typeName ? [images?.typeName] : undefined,
    images?.subTypeName ? [images?.subTypeName] : undefined,
  ].filter(Boolean);

  return (
    <>
      {(images?.data ?? []).map((image) => {
        return (
          <Group key={image.id}>
            {!dHViewInfo?.isHideTableInfo && (
              <TableKonva
                startX={images.startX - dHViewConfigs.displacementX}
                startY={
                  isNumber(images.minHeight)
                    ? images.minHeight -
                      (tableData.length + 1) * dHViewConfigs.cellHeight
                    : 0
                }
                data={tableData as any}
                cellHeight={dHViewConfigs.cellHeight}
                cellWidth={dHViewConfigs.cellWidth}
                fontSize={dHViewConfigs.fontSize}
                strokeWidth={dHViewConfigs.strokeWidth}
              />
            )}

            {dHViewInfo?.isShowTextDepth && (
              <Text
                x={
                  images.startX -
                  (String(image.depthFrom).length + 2) *
                    (dHViewConfigs.depthFromSize / 2) -
                  0.08
                }
                y={image.depthFrom - dHViewConfigs.depthFromSize / 2}
                text={`${image.depthFrom} -`}
                fontSize={dHViewConfigs.depthFromSize}
                fill="black"
              ></Text>
            )}

            <ZoomableImage
              key={image.displayUrl}
              src={image.displayUrl}
              x={images.startX}
              y={image.depthFrom}
              width={image.width}
              height={image.height}
              onClick={() => handlePreview(image)}
              onMouseEnter={handleImageMouseEnter}
              onMouseLeave={handleImageMouseLeave}
              onMouseMove={handleImageMouseMove}
              {...images.uiProps}
            />

            {dHViewInfo?.isShowTextDepth && (
              <Text
                x={
                  images.startX -
                  (String(image.depthTo).length + 2) *
                    (dHViewConfigs.depthToSize / 2) -
                  0.08
                }
                y={image.depthTo - dHViewConfigs.depthToSize / 2}
                text={`${image.depthTo} -`}
                fontSize={dHViewConfigs.depthToSize}
                fill="black"
              ></Text>
            )}
          </Group>
        );
      })}

      {/* Tooltip component rendered via portal */}
      <ImageTooltip
        visible={tooltip.visible}
        position={tooltip.position}
        drillhole={images?.drillhole || ""}
        typeName={images?.typeName}
        subTypeName={images?.subTypeName}
      />
    </>
  );
};

export default memo(ImageColumn);
