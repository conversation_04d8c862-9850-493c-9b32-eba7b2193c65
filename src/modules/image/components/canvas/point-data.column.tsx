import { isNumber } from "lodash";
import { memo } from "react";
import { Group, Line } from "react-konva";

import { useAppSelector } from "@/common/vendors/redux/store/hook";

import { DrillHoleViewStack } from "../../model/entities/drillhole.config";
import { selectDHViewConfig, selectDHViewInfo } from "../../redux/imageSlice";
import TableKonva from "./table.konva";
interface Props {
  dataPoints: DrillHoleViewStack;
}

const PointDataColumn = ({ dataPoints }: Props) => {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const dHViewInfo = useAppSelector(selectDHViewInfo);

  const points = (dataPoints?.data ?? []).reduce(
    (acc: any[], point) => acc.concat([point.y, point.x]),
    [],
  );

  const tableData = [[dataPoints?.drillhole], [dataPoints?.extraInfo]];

  return (
    <Group>
      {!dHViewInfo?.isHideTableInfo && (
        <TableKonva
          startX={dataPoints.startX}
          startY={
            isNumber(dataPoints.minHeight)
              ? dataPoints.minHeight -
                (tableData.length + 1) * dHViewConfigs.cellHeight
              : 0
          }
          data={tableData}
          cellHeight={dHViewConfigs.cellHeight}
          cellWidth={dHViewConfigs.pointDataWidth / 2.3}
          fontSize={dHViewConfigs.fontSize}
          strokeWidth={dHViewConfigs.strokeWidth}
        />
      )}

      <Line
        x={dataPoints.startX}
        points={points}
        stroke="black"
        strokeWidth={dHViewConfigs?.lineStrokeWidth ?? 0.03}
        lineJoin="round"
        lineCap="round"
        tension={0.05} // smooth the line
      />
    </Group>
  );
};

export default memo(PointDataColumn);
