import { isNumber } from "lodash";
import { memo } from "react";
import { Group, Rect, Text } from "react-konva";

import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { getContrastingTextColor } from "@/utils/color.utils";

import { DrillHoleViewStack } from "../../model/entities/drillhole.config";
import { selectDHViewConfig, selectDHViewInfo } from "../../redux/imageSlice";
import TableKonva from "./table.konva";

interface Props {
  intervals: DrillHoleViewStack;
}

const IntervalGeologyColumn = ({ intervals }: Props) => {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const dHViewInfo = useAppSelector(selectDHViewInfo);
  const { selectedGeologyFieldId } = useAppSelector((state) => state.images);

  const tableData = [
    [intervals?.drillhole],
    [intervals?.extraInfo],
    ["Rock Type"],
  ];

  return (
    <Group>
      {!dHViewInfo?.isHideTableInfo && (
        <TableKonva
          startX={intervals.startX}
          startY={
            isNumber(intervals.minHeight)
              ? intervals.minHeight -
                (tableData.length + 1) * dHViewConfigs.cellHeight
              : 0
          }
          data={tableData}
          cellHeight={dHViewConfigs.cellHeight}
          cellWidth={dHViewConfigs.pointDataWidth / 2.3}
          fontSize={dHViewConfigs.fontSize}
          strokeWidth={dHViewConfigs.strokeWidth}
        />
      )}
      {(intervals.data ?? []).map((interval) => {
        if (!selectedGeologyFieldId) {
          return null;
        }

        // Check if selectedGeologyFieldId is a geology suite field ID (number) or legacy field type (RockType enum)
        const rockTypeInfo = interval.dataEntryValues.find((entry) => {
          return entry.geologysuiteFieldId === selectedGeologyFieldId;
        });

        if (
          !isNumber(interval?.depthTo) ||
          !isNumber(interval?.depthFrom) ||
          !rockTypeInfo
        ) {
          return null;
        }

        return (
          <Group key={intervals?.id}>
            <Rect
              x={intervals.startX}
              y={interval.depthFrom}
              width={intervals.maxWidth}
              height={interval.depthTo - interval.depthFrom}
              fill={rockTypeInfo?.rockType?.rockStyle?.fillColor}
            />
            <Text
              text={rockTypeInfo?.rockType?.code}
              fontSize={dHViewConfigs.fontSize}
              x={
                intervals.startX +
                intervals.maxWidth / 2 -
                (rockTypeInfo?.rockType?.code?.length *
                  dHViewConfigs.fontSize) /
                  2.6
              }
              y={
                interval.depthFrom +
                (interval.depthTo - interval.depthFrom) / 2 -
                dHViewConfigs.fontSize / 2.6
              }
              fill={getContrastingTextColor(
                rockTypeInfo?.rockType?.rockStyle?.fillColor,
              )}
            ></Text>

            {dHViewInfo?.isShowTextDepth && (
              <Text
                x={
                  intervals.startX -
                  (String(interval.depthFrom).length + 2) *
                    (dHViewConfigs.depthFromSize / 2) -
                  0.08
                }
                y={interval.depthFrom - dHViewConfigs.depthFromSize / 2}
                text={`${interval.depthFrom} -`}
                fontSize={dHViewConfigs.depthToSize}
                fill="black"
              ></Text>
            )}

            {dHViewInfo?.isShowTextDepth && (
              <Text
                x={
                  intervals.startX -
                  (String(interval.depthTo).length + 2) *
                    (dHViewConfigs.depthFromSize / 2) -
                  0.08
                }
                y={interval.depthTo - dHViewConfigs.depthFromSize / 2}
                text={`${interval.depthTo} -`}
                fontSize={dHViewConfigs.depthToSize}
                fill="black"
              ></Text>
            )}
          </Group>
        );
      })}
    </Group>
  );
};

export default memo(IntervalGeologyColumn);
